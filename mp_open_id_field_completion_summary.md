# sys_user表mp_open_id字段完善总结

## 完成的修改

### 1. 后端Java实体类修改

#### 1.1 SysUser.java (实体类)
- 添加了 `private String mpOpenId;` 字段
- 添加了相应的注释：`微信小程序openId`

#### 1.2 SysUserVo.java (视图对象)
- 添加了 `private String mpOpenId;` 字段
- 添加了相应的注释：`微信小程序openId`

#### 1.3 SysUserBo.java (业务对象)
- 添加了 `private String mpOpenId;` 字段
- 添加了相应的注释：`微信小程序openId`

#### 1.4 SysUserQuery.java (查询对象)
- 添加了 `private String mpOpenId;` 字段
- 添加了相应的注释：`微信小程序openId`

#### 1.5 SysUserExportVo.java (导出对象)
- 添加了 `private String mpOpenId;` 字段
- 添加了Excel注解：`@ExcelProperty(value = "微信小程序openId")`

#### 1.6 SysUserImportVo.java (导入对象)
- 添加了 `private String mpOpenId;` 字段
- 添加了Excel注解：`@ExcelProperty(value = "微信小程序openId")`

### 2. Service层修改

#### 2.1 ISysUserService.java (接口)
- 添加了 `selectUserByMpOpenId(String mpOpenId)` 方法
- 添加了 `checkMpOpenIdUnique(SysUserBo user)` 方法

#### 2.2 SysUserServiceImpl.java (实现类)
- 实现了 `selectUserByMpOpenId` 方法，支持根据微信小程序openId查询用户
- 实现了 `checkMpOpenIdUnique` 方法，校验微信小程序openId的唯一性

### 3. Controller层修改

#### 3.1 SysUserController.java
- 在新增用户方法中添加了mp_open_id唯一性校验
- 在修改用户方法中添加了mp_open_id唯一性校验

### 4. 数据库映射文件修改

#### 4.1 SysUserMapper.xml
- 更新了 `selectSysUserVo` SQL片段，包含mp_open_id字段
- 更新了 `queryList` 查询，包含mp_open_id字段
- 更新了 `selectPageUserList` 查询，包含mp_open_id字段
- 更新了 `selectUserExportList` 查询，包含mp_open_id字段
- 更新了 `selectUserList` 查询，包含mp_open_id字段
- 更新了 `selectSafeUserById` 查询，包含mp_open_id字段
- 在查询条件中添加了mp_open_id的精确匹配支持

### 5. 前端TypeScript类型定义修改

#### 5.1 userModel.ts
- 在 `SysUserQuery` 接口中添加了 `mpOpenId?: string;`
- 在 `SysUserVo` 接口中添加了 `mpOpenId?: string;`

### 6. 数据库脚本

#### 6.1 update_sys_user_add_mp_open_id.sql
- 创建了数据库字段添加脚本
- 支持MySQL、PostgreSQL、Oracle三种数据库
- 包含字段添加和索引创建

## 功能特性

### 1. 字段特性
- 字段类型：varchar(100)
- 允许为空：是
- 唯一性：如果有值则必须唯一
- 索引：已创建索引提高查询性能

### 2. 业务功能
- 支持根据微信小程序openId查询用户
- 支持在用户列表中按mp_open_id筛选
- 支持新增/修改用户时的唯一性校验
- 支持用户数据的导入导出

### 3. 查询支持
- 精确匹配查询：`mpOpenId = 'xxx'`
- 列表查询中包含mp_open_id字段
- 详情查询中包含mp_open_id字段

## 使用方法

### 1. 数据库准备
```sql
-- 执行数据库脚本
ALTER TABLE sys_user ADD COLUMN mp_open_id varchar(100) DEFAULT NULL COMMENT '微信小程序openId';
CREATE INDEX idx_sys_user_mp_open_id ON sys_user(mp_open_id);
```

### 2. API使用示例
```java
// 根据微信小程序openId查询用户
SysUserVo user = userService.selectUserByMpOpenId("wx_openid_123");

// 校验微信小程序openId唯一性
boolean isUnique = userService.checkMpOpenIdUnique(userBo);
```

### 3. 查询参数示例
```
GET /system/user/list?mpOpenId=wx_openid_123
```

## 注意事项
1. mp_open_id字段允许为空，但如果有值则必须唯一
2. 查询时使用精确匹配，不支持模糊查询
3. 导入导出功能中mp_open_id字段为可选字段
4. 建议在微信小程序登录时使用此字段关联用户
