{"private": "true", "name": "@tencent/tdesign-vue-next-starter", "version": "0.12.0", "description": "Ruoyi-Tdesign多租户管理系统", "type": "module", "scripts": {"dev:mock": "vite --open --mode mock", "dev": "vite --open --mode development", "dev:linux": "vite --mode development", "build:test": "vite build --mode test --minify false", "build": "vite build --mode release", "build:type": "vue-tsc --noEmit", "build:site": "vue-tsc --noEmit && vite build --mode site", "preview": "vite preview", "stylelint": "stylelint src/**/*.{html,vue,sass,less}", "stylelint:fix": "stylelint --fix src/**/*.{html,vue,css,sass,less}", "site:preview": "npm run build && cp -r dist _site", "test": "echo \"no test specified,work in process\"", "test:coverage": "echo \"no test:coverage specified,work in process\""}, "dependencies": {"@tinymce/tinymce-vue": "~5.1.1", "@vueuse/core": "^13.1.0", "axios": "^1.9.0", "crypto-js": "^4.2.0", "dayjs": "^1.11.13", "echarts": "5.4.3", "file-saver": "^2.0.5", "fuse.js": "^7.1.0", "image-conversion": "^2.1.1", "js-cookie": "^3.0.5", "jsencrypt": "^3.3.2", "lodash": "^4.17.21", "nprogress": "^0.2.0", "pinia": "~3.0.2", "pinia-plugin-persistedstate": "~4.2.0", "prismjs": "^1.30.0", "qrcode.vue": "^3.6.0", "qs": "^6.14.0", "tdesign-icons-vue-next": "0.3.6", "tdesign-vue-next": "1.12.0", "tinymce": "^7.8.0", "tvision-color": "^1.6.0", "vue": "~3.5.13", "vue-cropper": "^1.1.4", "vue-i18n": "^9.14.0", "vue-json-pretty": "^2.4.0", "vue-router": "~4.5.1"}, "devDependencies": {"@commitlint/cli": "^19.4.1", "@commitlint/config-conventional": "^19.8.0", "@types/crypto-js": "^4.2.2", "@types/echarts": "^4.9.22", "@types/js-cookie": "^3.0.6", "@types/lodash": "^4.17.16", "@types/nprogress": "^0.2.3", "@types/prismjs": "^1.26.5", "@types/qs": "^6.9.18", "@vitejs/plugin-vue": "^5.2.3", "@vitejs/plugin-vue-jsx": "^4.1.2", "commitizen": "^4.3.0", "cz-conventional-changelog": "^3.3.0", "less": "^4.3.0", "lint-staged": "^15.2.2", "mockjs": "^1.1.0", "postcss-html": "^1.8.0", "postcss-less": "^6.0.0", "prettier": "^3.3.3", "rollup-plugin-copy": "^3.5.0", "stylelint": "~16.3.1", "stylelint-config-standard": "^36.0.0", "stylelint-order": "~6.0.4", "typescript": "~5.8.3", "unocss": "^0.65.4", "unplugin-auto-import": "^19.1.2", "unplugin-vue-components": "^28.5.0", "vite": "6.3.4", "vite-plugin-compression": "^0.5.1", "vite-plugin-mock": "^3.0.2", "vite-plugin-prismjs": "^0.0.11", "vite-svg-loader": "^5.1.0", "vue-tsc": "2.2.10"}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "lint-staged": {"*.{js,jsx,vue,ts,tsx}": ["prettier --write"], "*.{html,vue,css,sass,less}": ["npm run stylelint:fix"]}, "engines": {"node": ">=18.18.0"}}