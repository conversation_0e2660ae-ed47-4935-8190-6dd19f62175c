/**
 * v-hasRole 角色权限处理
 * Copyright (c) 2019 ruoyi
 */

import { useUserStore } from '@/store/modules/user';

export default {
  mounted(el: any, binding: any) {
    const { value } = binding;
    const { roles } = useUserStore();

    if (value && value instanceof Array && value.length > 0) {
      const roleFlag = value;

      const hasRole = roles.some((role) => {
        return role === 'superadmin' || role === 'admin' || roleFlag.includes(role);
      });

      if (!hasRole) {
        // eslint-disable-next-line no-unused-expressions
        el.parentNode && el.parentNode.removeChild(el);
      }
    } else {
      throw new Error(`请设置角色权限标签值`);
    }
  },
};
