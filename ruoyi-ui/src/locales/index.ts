import { useLocalStorage, usePreferredLanguages } from '@vueuse/core';
import type { DropdownOption } from 'tdesign-vue-next';
import { computed } from 'vue';
import { createI18n } from 'vue-i18n';

// 导入语言文件
const langModules = import.meta.glob('./lang/*/index.ts', { eager: true });

const langModuleMap = new Map<string, Object>();

export const langCode: Array<string> = [];

export const localeConfigKey = 'tdesign-starter-locale';

// 获取浏览器默认语言环境
const languages = usePreferredLanguages();

// 生成语言模块列表
const generateLangModuleMap = () => {
  const fullPaths = Object.keys(langModules);
  fullPaths.forEach((fullPath) => {
    const k = fullPath.replace('./lang', '');
    const startIndex = 1;
    const lastIndex = k.lastIndexOf('/');
    const code = k.substring(startIndex, lastIndex);
    langCode.push(code);
    langModuleMap.set(code, langModules[fullPath]);
  });
};

// 导出 Message
const importMessages = computed(() => {
  generateLangModuleMap();

  const message: Recordable = {};
  langModuleMap.forEach((value: any, key) => {
    message[key] = value.default;
  });
  return message;
});

// 标准化语言代码格式
const normalizeLocale = (locale: string): string => {
  if (!locale) return 'zh_CN';

  // 如果只有语言代码，补充国家代码
  if (locale === 'zh') return 'zh_CN';
  if (locale === 'en') return 'en_US';

  // 如果已经是标准格式，直接返回
  if (locale.includes('_')) return locale;

  // 默认返回中文
  return 'zh_CN';
};

const getInitialLocale = (): string => {
  const stored = useLocalStorage(localeConfigKey, 'zh_CN').value;
  const browser = languages.value[0];
  return normalizeLocale(stored || browser || 'zh_CN');
};

export const i18n = createI18n({
  legacy: false,
  locale: getInitialLocale(),
  fallbackLocale: 'zh_CN',
  messages: importMessages.value,
  globalInjection: true,
});

export const langList = computed(() => {
  if (langModuleMap.size === 0) generateLangModuleMap();

  const list: DropdownOption[] = [];
  langModuleMap.forEach((value: any, key) => {
    list.push({
      content: value.default.lang,
      value: key,
    });
  });

  return list;
});

// @ts-ignore
export const { t } = i18n.global;

export default i18n;
