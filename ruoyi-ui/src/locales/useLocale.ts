import { useLocalStorage } from '@vueuse/core';
import { computed } from 'vue';
import { useI18n } from 'vue-i18n';

import { t } from '@/locales';
import { i18n, langCode, localeConfigKey } from '@/locales/index';
import message from '@/plugins/modal';

export function useLocale() {
  const { locale } = useI18n({ useScope: 'global' });
  function changeLocale(lang: string) {
    // 标准化语言代码格式
    const normalizeLocale = (locale: string): string => {
      if (!locale) return 'zh_CN';

      // 如果只有语言代码，补充国家代码
      if (locale === 'zh') return 'zh_CN';
      if (locale === 'en') return 'en_US';

      // 如果已经是标准格式，直接返回
      if (locale.includes('_')) return locale;

      // 默认返回中文
      return 'zh_CN';
    };

    const normalizedLang = normalizeLocale(lang);

    // 如果切换的语言不在对应语言文件里则默认为简体中文
    if (!langCode.includes(normalizedLang)) {
      lang = 'zh_CN';
    } else {
      lang = normalizedLang;
    }

    locale.value = lang;
    useLocalStorage(localeConfigKey, 'zh_CN').value = lang;
    message.msgSuccess(t('constants.switchLanguage'));
  }

  const getComponentsLocale = computed(() => {
    return i18n.global.getLocaleMessage(locale.value).componentsLocale;
  });

  return {
    changeLocale,
    getComponentsLocale,
    locale,
  };
}
