.text-center {
  text-align: center
}

.list-group-striped > .list-group-item {
  border-left: 0;
  border-right: 0;
  border-radius: 0;
  padding-left: 0;
  padding-right: 0;
}

.list-group {
  padding-left: 0;
  list-style: none;
}

.list-group-item {
  border-bottom: 1px solid #e7eaec;
  border-top: 1px solid #e7eaec;
  margin-bottom: -1px;
  padding: 11px 0;
  font-size: 13px;
}

.pull-right {
  float: right !important;
}

.clearfix {
  &::after {
    visibility: hidden;
    display: block;
    font-size: 0;
    content: " ";
    clear: both;
    height: 0;
  }
}

/* tree border */
.tree-border {
  margin-top: 5px;
  border: 1px solid #e5e6e7;
  background: #FFF none;
  border-radius: 4px;
  width: 100%;
}

.overlay-options {
  display: inline-block !important;
  font-size: 18px !important;
}

.content-scrollbar {
  overflow-y: scroll;

  &::-webkit-scrollbar {
    width: 12px;
    background: transparent;
  }

  &::-webkit-scrollbar-thumb {
    min-height: 40px;
    border-radius: 6px;
    border: 3px solid transparent;
    background-clip: content-box;
    background-color: var(--td-scrollbar-color);
  }
}

// 弹窗内容最大高度
.dialog-content-max-height {
  max-height: calc(100vh - var(--td-comp-paddingTB-xxl) - var(--td-comp-paddingTB-l) - 24px - 140px)
}

.gen-option > span {
  width: 100%;
}

// 富文本弹出层
.tox-tinymce-aux {
  z-index: 3000 !important;
}

.form-detail {
  .t-form__label {
    color: var(--td-text-color-placeholder)
  }
}

// 图片预览禁止选中
.t-image-viewer-preview-image .t-image-viewer__modal-pic .t-image-viewer__modal-box .t-image-viewer__modal-image {
  -webkit-user-select: none;  /* Safari */
  -moz-user-select: none;    /* Firefox */
  -ms-user-select: none;      /* IE10+/Edge */
  user-select: none;          /* Standard syntax */
}
