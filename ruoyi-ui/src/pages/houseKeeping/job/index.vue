<template>
  <t-card>
    <t-space direction="vertical" style="width: 100%">
      <t-form v-show="showSearch" ref="queryRef" :data="queryParams" layout="inline" reset-type="initial" label-width="calc(4em + 12px)">
        <t-form-item label="服务类型" name="categoryId">
          <t-input v-model="queryParams.categoryId" placeholder="请输入服务类型" clearable @enter="handleQuery" />
        </t-form-item>
        <t-form-item label="岗位名称" name="name">
          <t-input v-model="queryParams.name" placeholder="请输入岗位名称" clearable @enter="handleQuery" />
        </t-form-item>
        <t-form-item label-width="0px">
          <t-button theme="primary" @click="handleQuery">
            <template #icon> <search-icon /></template>
            搜索
          </t-button>
          <t-button theme="default" @click="resetQuery">
            <template #icon> <refresh-icon /></template>
            重置
          </t-button>
        </t-form-item>
      </t-form>

      <t-table
        v-model:column-controller-visible="columnControllerVisible"
        :loading="loading"
        hover
        row-key="jobId"
        :data="jobList"
        :columns="columns"
        :selected-row-keys="ids"
        select-on-row-click
        :pagination="pagination"
        :column-controller="{
          hideTriggerButton: true,
        }"
        :sort="sort"
        show-sort-column-bg-color
        @sort-change="handleSortChange"
        @select-change="handleSelectionChange"
      >
        <template #topContent>
          <t-row>
            <t-col flex="auto">
              <t-button v-hasPermi="['houseKeeping:job:add']" theme="primary" @click="handleAdd">
                <template #icon> <add-icon /></template>
                新增
              </t-button>
              <t-button
                v-hasPermi="['houseKeeping:job:edit']"
                theme="default"
                variant="outline"
                :disabled="single"
                @click="handleUpdate()"
              >
                <template #icon> <edit-icon /> </template>
                修改
              </t-button>
              <t-button
                v-hasPermi="['houseKeeping:job:remove']"
                theme="danger"
                variant="outline"
                :disabled="multiple"
                @click="handleDelete()"
              >
                <template #icon> <delete-icon /> </template>
                删除
              </t-button>
              <t-button v-hasPermi="['houseKeeping:job:export']" theme="default" variant="outline" @click="handleExport">
                <template #icon> <download-icon /> </template>
                导出
              </t-button>
              <span class="selected-count">已选 {{ ids.length }} 项</span>
            </t-col>
            <t-col flex="none">
              <t-button theme="default" shape="square" variant="outline" @click="showSearch = !showSearch">
                <template #icon> <search-icon /> </template>
              </t-button>
              <t-button theme="default" variant="outline" @click="columnControllerVisible = true">
                <template #icon> <setting-icon /> </template>
                列配置
              </t-button>
            </t-col>
          </t-row>
        </template>
        <template #image="{ row }">
          <image-preview :src="row.image" :width="60" :height="60" />
        </template>
        <template #operation="{ row }">
          <t-space :size="8" break-line>
            <my-link v-hasPermi="['houseKeeping:job:query']" @click.stop="handleDetail(row)">
              <template #prefix-icon><browse-icon /></template>详情
            </my-link>
            <my-link v-hasPermi="['houseKeeping:job:edit']" @click.stop="handleUpdate(row)">
              <template #prefix-icon><edit-icon /></template>修改
            </my-link>
            <my-link v-hasPermi="['houseKeeping:job:remove']" size="small" @click.stop="handleDelete(row)">
              <template #prefix-icon><delete-icon /></template>删除
            </my-link>
          </t-space>
        </template>
      </t-table>
    </t-space>

    <!-- 添加或修改岗位管理对话框 -->
    <t-dialog
      v-model:visible="open"
      :header="title"
      destroy-on-close
      :close-on-overlay-click="false"
      width="min(900px, 100%)"
      attach="body"
      :confirm-btn="{
        loading: buttonLoading,
      }"
      @confirm="jobRef.submit()"
    >
      <t-loading :loading="buttonLoading" size="small">
        <t-form
          ref="jobRef"
          label-align="right"
          :data="form"
          :rules="rules"
          label-width="calc(5em + 41px)"
          scroll-to-first-error="smooth"
          @submit="submitForm"
        >
          <t-row :gutter="[24,24]">
            <t-col :span="6">
              <t-form-item label="服务类型" name="categoryId">
                <t-select
                  v-model="form.categoryId"
                  :options="categoryOptions"
                  placeholder="请选择家政服务类型"
                  clearable
                ></t-select>
              </t-form-item>
            </t-col>
            <t-col :span="6">
              <t-form-item label="岗位名称" name="name">
                <t-input v-model="form.name" placeholder="请输入岗位名称" clearable />
              </t-form-item>
            </t-col>
            <t-col :span="6">
              <t-form-item label="图片" name="image">
                <image-upload v-model="form.image" :limit="1" />
              </t-form-item>
            </t-col>

            <t-col :span="6">
              <t-form-item label="状态" name="status">
                <t-radio-group v-model="form.status">
                  <t-radio :value="0">禁用</t-radio>
                  <t-radio :value="1">启用</t-radio>
                </t-radio-group>
              </t-form-item>
            </t-col>
            <t-col :span="6">
              <t-form-item label="排序" name="sort">
                <t-input-number v-model="form.sort" placeholder="请输入" />
              </t-form-item>
            </t-col>
            <t-col :span="12">
              <t-form-item label="岗位描述" name="description">
                <editor v-model="form.description" />
              </t-form-item>
            </t-col>
            <t-col :span="12">
              <t-form-item label="岗位要求" name="requirements">
                <editor v-model="form.requirements" />
              </t-form-item>
            </t-col>
          </t-row>
        </t-form>
      </t-loading>
    </t-dialog>

    <!-- 岗位管理详情 -->
    <t-dialog
      v-model:visible="openView"
      header="岗位管理详情"
      placement="center"
      width="min(700px, 100%)"
      attach="body"
      :footer="false"
    >
      <my-descriptions :loading="openViewLoading">
        <t-descriptions-item label="岗位编号">{{ form.jobId }}</t-descriptions-item>
        <t-descriptions-item label="服务类型">{{ form.categoryId }}</t-descriptions-item>
        <t-descriptions-item label="岗位名称">{{ form.name }}</t-descriptions-item>
        <t-descriptions-item label="岗位描述" :span="2">
          <editor-preview :html-text="form.description" />
        </t-descriptions-item>
        <t-descriptions-item label="图片">
          <image-preview :src="form.image" :width="60" :height="60" />
        </t-descriptions-item>
        <t-descriptions-item label="岗位要求" :span="2">
          <editor-preview :html-text="form.requirements" />
        </t-descriptions-item>
        <t-descriptions-item label="状态">{{ form.status }}</t-descriptions-item>
        <t-descriptions-item label="排序">{{ form.sort }}</t-descriptions-item>
        <t-descriptions-item label="创建时间">{{ parseTime(form.createTime) }}</t-descriptions-item>
      </my-descriptions>
    </t-dialog>
  </t-card>
</template>
<script setup>
defineOptions({
  name: 'Job',
});
import {
  AddIcon,
  BrowseIcon,
  DeleteIcon,
  DownloadIcon,
  EditIcon,
  RefreshIcon,
  SearchIcon,
  SettingIcon,
} from 'tdesign-icons-vue-next';
import { computed, getCurrentInstance, ref, onMounted } from 'vue';
import { ArrayOps } from '@/utils/array';
import { listJob, getJob, delJob, addJob, updateJob } from '@/api/houseKeeping/job';
import Editor from '@/components/editor/index.vue';
import EditorPreview from '@/components/editor-preview/index.vue';
import { listCategory } from '@/api/houseKeeping/category.ts'

const { proxy } = getCurrentInstance();
const openView = ref(false);
const openViewLoading = ref(false);
const jobRef = ref();
const open = ref(false);
const buttonLoading = ref(false);
const title = ref('');
const jobList = ref([]);
const loading = ref(false);
const columnControllerVisible = ref(false);
const showSearch = ref(true);
const total = ref(0);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const sort = ref();
const categoryList = ref([])
const categoryOptions = ref([])


const getCategoryList = async () => {
  const response = await listCategory({
    pageNum: 1,
    pageSize: 999
  })
  if(response.code !==200){
    await MessagePlugin.error(response.msg || '加载服务类型失败')
    return
  }
  categoryList.value = response.rows || []
  categoryOptions.value = categoryList.value.map(item => {
    return {
      label: item.name,
      value: item.categoryId
    }
  }) || []
}

onMounted(async () => {
  await getCategoryList()
})

// 校验规则
const rules = ref({
  categoryId: [{ max: 255, message: '服务类型不能超过255个字符' }],
  name: [{ max: 255, message: '岗位名称不能超过255个字符' }],
  image: [{ max: 255, message: '图片不能超过255个字符' }],
});

// 列显隐信息
const columns = ref([
  { title: `选择列`, colKey: 'row-select', type: 'multiple', width: 50, align: 'center' },
  { title: `服务类型`, colKey: 'categoryId', align: 'center' },
  { title: `岗位名称`, colKey: 'name', align: 'center' },
  { title: `岗位描述`, colKey: 'description', align: 'center' },
  { title: `图片`, colKey: 'image', align: 'center', width: 100 },
  { title: `岗位要求`, colKey: 'requirements', align: 'center' },
  { title: `状态`, colKey: 'status', align: 'center' },
  { title: `排序`, colKey: 'sort', align: 'center' },
  { title: `创建时间`, colKey: 'createTime', align: 'center', minWidth: 112, width: 180, sorter: true },
  { title: `操作`, colKey: 'operation', align: 'center', width: 180 },
]);
// 提交表单对象
const form = ref({});
// 查询对象
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  categoryId: undefined,
  name: undefined,
});
// 分页
const pagination = computed(() => {
  return {
    current: queryParams.value.pageNum,
    pageSize: queryParams.value.pageSize,
    total: total.value,
    showJumper: true,
    onChange: (pageInfo) => {
      queryParams.value.pageNum = pageInfo.current;
      queryParams.value.pageSize = pageInfo.pageSize;
      getList();
    },
  };
});

/** 查询岗位管理列表 */
function getList() {
  loading.value = true;
  listJob(queryParams.value)
    .then((response) => {
      jobList.value = response.rows;
      total.value = response.total;
    })
    .finally(() => (loading.value = false));
}

// 表单重置
function reset() {
  form.value = {};
  proxy.resetForm('jobRef');
  form.value.status = 1
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm('queryRef');
  queryParams.value.pageNum = 1;
  handleSortChange(null);
}

/** 排序触发事件 */
function handleSortChange(value) {
  sort.value = value;
  if (Array.isArray(value)) {
    queryParams.value.orderByColumn = value.map((item) => item.sortBy).join(',');
    queryParams.value.isAsc = value.map((item) => (item.descending ? 'descending' : 'ascending')).join(',');
  } else {
    queryParams.value.orderByColumn = value?.sortBy;
    queryParams.value.isAsc = value?.descending ? 'descending' : 'ascending';
  }
  getList();
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
  ids.value = selection;
  single.value = selection.length !== 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = '添加岗位管理';
}

/** 详情按钮操作 */
function handleDetail(row) {
  reset();
  openView.value = true;
  openViewLoading.value = true;
  const jobId = row.jobId;
  getJob(jobId).then((response) => {
    form.value = response.data;
    openViewLoading.value = false;
  });
}

/** 修改按钮操作 */
function handleUpdate(row) {
  buttonLoading.value = true;
  reset();
  open.value = true;
  title.value = '修改岗位管理';
  const jobId = row?.jobId || ids.value.at(0);
  getJob(jobId).then((response) => {
    buttonLoading.value = false;
    form.value = response.data;
  });
}

/** 提交表单 */
function submitForm({ validateResult, firstError }) {
  if (validateResult === true) {
    buttonLoading.value = true;
    const msgLoading = proxy.$modal.msgLoading('提交中...');
    if (form.value.jobId) {
      updateJob(form.value)
        .then(() => {
          proxy.$modal.msgSuccess('修改成功');
          open.value = false;
          getList();
        })
        .finally(() => {
          buttonLoading.value = false;
          proxy.$modal.msgClose(msgLoading);
        });
    } else {
      addJob(form.value)
        .then(() => {
          proxy.$modal.msgSuccess('新增成功');
          open.value = false;
          getList();
        })
        .finally(() => {
          buttonLoading.value = false;
          proxy.$modal.msgClose(msgLoading);
        });
    }
  } else {
    proxy.$modal.msgError(firstError);
  }
}

/** 删除按钮操作 */
function handleDelete(row) {
  const jobIds = row?.jobId || ids.value;
  proxy.$modal.confirm(`是否确认删除岗位管理编号为${jobIds}的数据项？`, () => {
    const msgLoading = proxy.$modal.msgLoading('正在删除中...');
    return delJob(jobIds)
      .then(() => {
        ids.value = ArrayOps.fastDeleteElement(ids.value, jobIds);
        getList();
        proxy.$modal.msgSuccess('删除成功');
      })
      .finally(() => {
        proxy.$modal.msgClose(msgLoading);
      });
  });
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download(
    'houseKeeping/job/export',
    {
      ...queryParams.value,
    },
    `job_${new Date().getTime()}.xlsx`,
  );
}

getList();
</script>
