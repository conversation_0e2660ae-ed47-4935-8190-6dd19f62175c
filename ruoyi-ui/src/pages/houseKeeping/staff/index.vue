<template>
  <t-card>
    <t-space direction="vertical" style="width: 100%">
      <t-form
        v-show="showSearch"
        ref="queryRef"
        :data="queryParams"
        layout="inline"
        reset-type="initial"
        label-width="calc(4em + 12px)"
      >
        <t-form-item label="姓名" name="name">
          <t-input v-model="queryParams.name" placeholder="请输入姓名" clearable @enter="handleQuery" />
        </t-form-item>
        <t-form-item label="职称" name="title">
          <t-input v-model="queryParams.title" placeholder="请输入职称" clearable @enter="handleQuery" />
        </t-form-item>
        <t-form-item label="服务类型" name="category">
          <t-input v-model="queryParams.category" placeholder="请输入服务类型" clearable @enter="handleQuery" />
        </t-form-item>
        <t-form-item label="岗位" name="job">
          <t-input v-model="queryParams.job" placeholder="请输入岗位" clearable @enter="handleQuery" />
        </t-form-item>
        <t-form-item label="状态" name="status">
          <t-input v-model="queryParams.status" placeholder="请输入状态" clearable @enter="handleQuery" />
        </t-form-item>
        <t-form-item label="服务标签" name="tag">
          <t-input v-model="queryParams.tag" placeholder="请输入服务标签" clearable @enter="handleQuery" />
        </t-form-item>
        <t-form-item label="用户ID" name="userId">
          <t-input v-model="queryParams.userId" placeholder="请输入用户ID" clearable @enter="handleQuery" />
        </t-form-item>
        <t-form-item label="认证状态" name="auth">
          <t-select v-model="queryParams.auth" placeholder="请选择认证状态" clearable>
            <t-option value="0" label="未认证" />
            <t-option value="1" label="已认证" />
          </t-select>
        </t-form-item>
        <t-form-item label-width="0px">
          <t-button theme="primary" @click="handleQuery">
            <template #icon> <search-icon /></template>
            搜索
          </t-button>
          <t-button theme="default" @click="resetQuery">
            <template #icon> <refresh-icon /></template>
            重置
          </t-button>
        </t-form-item>
      </t-form>

      <t-table
        v-model:column-controller-visible="columnControllerVisible"
        :loading="loading"
        hover
        row-key="staffId"
        :data="staffList"
        :columns="columns"
        :selected-row-keys="ids"
        select-on-row-click
        :pagination="pagination"
        :column-controller="{
          hideTriggerButton: true,
        }"
        :sort="sort"
        show-sort-column-bg-color
        @sort-change="handleSortChange"
        @select-change="handleSelectionChange"
      >
        <template #topContent>
          <t-row>
            <t-col flex="auto">
              <t-button v-hasPermi="['houseKeeping:staff:add']" theme="primary" @click="handleAdd">
                <template #icon> <add-icon /></template>
                新增
              </t-button>
              <t-button
                v-hasPermi="['houseKeeping:staff:edit']"
                theme="default"
                variant="outline"
                :disabled="single"
                @click="handleUpdate()"
              >
                <template #icon> <edit-icon /> </template>
                修改
              </t-button>
              <t-button
                v-hasPermi="['houseKeeping:staff:remove']"
                theme="danger"
                variant="outline"
                :disabled="multiple"
                @click="handleDelete()"
              >
                <template #icon> <delete-icon /> </template>
                删除
              </t-button>
              <t-button
                v-hasPermi="['houseKeeping:staff:export']"
                theme="default"
                variant="outline"
                @click="handleExport"
              >
                <template #icon> <download-icon /> </template>
                导出
              </t-button>
              <span class="selected-count">已选 {{ ids.length }} 项</span>
            </t-col>
            <t-col flex="none">
              <t-button theme="default" shape="square" variant="outline" @click="showSearch = !showSearch">
                <template #icon> <search-icon /> </template>
              </t-button>
              <t-button theme="default" variant="outline" @click="columnControllerVisible = true">
                <template #icon> <setting-icon /> </template>
                列配置
              </t-button>
            </t-col>
          </t-row>
        </template>
        <template #image="{ row }">
          <image-preview :src="row.image" :width="60" :height="60" />
        </template>
        <template #cert="{ row }">
          <image-preview :src="row.cert" :width="60" :height="60" />
        </template>
        <template #auth="{ row }">
          <t-tag v-if="row.auth === 1" theme="success">已认证</t-tag>
          <t-tag v-else theme="warning">未认证</t-tag>
        </template>
        <template #operation="{ row }">
          <t-space :size="8" break-line>
            <my-link v-hasPermi="['houseKeeping:staff:query']" @click.stop="handleDetail(row)">
              <template #prefix-icon><browse-icon /></template>详情
            </my-link>
            <my-link v-hasPermi="['houseKeeping:staff:edit']" @click.stop="handleUpdate(row)">
              <template #prefix-icon><edit-icon /></template>修改
            </my-link>
            <my-link v-hasPermi="['houseKeeping:staff:remove']" size="small" @click.stop="handleDelete(row)">
              <template #prefix-icon><delete-icon /></template>删除
            </my-link>
          </t-space>
        </template>
      </t-table>
    </t-space>

    <!-- 添加或修改服务人员管理对话框 -->
    <t-dialog
      v-model:visible="open"
      :header="title"
      destroy-on-close
      :close-on-overlay-click="false"
      width="min(850px, 100%)"
      attach="body"
      :confirm-btn="{
        loading: buttonLoading,
      }"
      @confirm="staffRef.submit()"
    >
      <t-loading :loading="buttonLoading" size="small">
        <t-form
          ref="staffRef"
          label-align="right"
          :data="form"
          :rules="rules"
          label-width="calc(5em + 41px)"
          scroll-to-first-error="smooth"
          @submit="submitForm"
        >
          <t-row :gutter="[24, 24]">
            <t-col :span="6">
              <t-form-item label="用户ID" name="userId">
                <t-select
                  v-model="form.userId"
                  :options="userOptions"
                  placeholder="请选择系统用户"
                  clearable
                ></t-select>
              </t-form-item>
            </t-col>
            <t-col :span="6">
              <t-form-item label="认证状态" name="auth">
                <t-radio-group v-model="form.auth">
                  <t-radio :value="0">未认证</t-radio>
                  <t-radio :value="1">已认证</t-radio>
                </t-radio-group>
              </t-form-item>
            </t-col>
            <t-col :span="6">
              <t-form-item label="姓名" name="name">
                <t-input v-model="form.name" placeholder="请输入姓名" clearable />
              </t-form-item>
            </t-col>
            <t-col :span="6">
              <t-form-item label="职称" name="title">
                <t-input v-model="form.title" placeholder="请输入职称" clearable />
              </t-form-item>
            </t-col>
            <t-col :span="6">
              <t-form-item label="性别" name="gender">
                <t-radio-group v-model="form.gender">
                  <t-radio :value="0">男</t-radio>
                  <t-radio :value="1">女</t-radio>
                </t-radio-group>
              </t-form-item>
            </t-col>
            <t-col :span="6">
              <t-form-item label="联系电话" name="phone">
                <t-input v-model="form.phone" placeholder="请输入联系电话" clearable />
              </t-form-item>
            </t-col>
            <t-col :span="6">
              <t-form-item label="年龄" name="age">
                <t-input-number v-model="form.age" placeholder="请输入" />
              </t-form-item>
            </t-col>
            <t-col :span="6">
              <t-form-item label="评分" name="star">
                <t-input-number v-model="form.star" placeholder="请输入" />
              </t-form-item>
            </t-col>
            <t-col :span="6">
              <t-form-item label="照片" name="image">
                <image-upload v-model="form.image" :limit="1" />
              </t-form-item>
            </t-col>
            <t-col :span="6">
              <t-form-item label="服务类型" name="category">
                <t-select
                  v-model="form.category"
                  :options="categoryOptions"
                  placeholder="请选择服务类型"
                  clearable
                  @change="getJobOptions"
                ></t-select>
              </t-form-item>
            </t-col>
            <t-col :span="6">
              <t-form-item label="岗位" name="job">
                <t-select
                  v-model="form.job"
                  :options="jobOptions"
                  placeholder="请选择岗位"
                  clearable

                ></t-select>
              </t-form-item>
            </t-col>
            <t-col :span="6">
              <t-form-item label="证书" name="cert">
                <image-upload v-model="form.cert" :limit="1" />
              </t-form-item>
            </t-col>
            <t-col :span="6">
              <t-form-item label="工作状态" name="status">
                <t-input v-model="form.status" placeholder="请输入状态" clearable />
              </t-form-item>
            </t-col>
            <t-col :span="12">
              <t-form-item label="服务标签" name="tag">
                <t-tag-input v-model="form.tag" clearable placeholder="请输入服务标签" @change="handleTagChange"/>
              </t-form-item>
            </t-col>
            <t-col :span="12">
              <t-form-item label="工作经验" name="experience">
                <t-textarea v-model="form.experience" placeholder="请输入工作经验" autosize />
              </t-form-item>
            </t-col>
            <t-col :span="12">
              <t-form-item label="工作经验详情" name="experienceDetail">
                <editor v-model="form.experienceDetail" />
              </t-form-item>
            </t-col>
          </t-row>
        </t-form>
      </t-loading>
    </t-dialog>

    <!-- 服务人员管理详情 -->
    <t-dialog
      v-model:visible="openView"
      header="服务人员管理详情"
      placement="center"
      width="min(700px, 100%)"
      attach="body"
      :footer="false"
    >
      <my-descriptions :loading="openViewLoading">
        <t-descriptions-item label="职员编号">{{ form.staffId }}</t-descriptions-item>
        <t-descriptions-item label="姓名">{{ form.name }}</t-descriptions-item>
        <t-descriptions-item label="职称">{{ form.title }}</t-descriptions-item>
        <t-descriptions-item label="性别">{{ form.gender }}</t-descriptions-item>
        <t-descriptions-item label="联系电话">{{ form.phone }}</t-descriptions-item>
        <t-descriptions-item label="年龄">{{ form.age }}</t-descriptions-item>
        <t-descriptions-item label="评分">{{ form.star }}</t-descriptions-item>
        <t-descriptions-item label="照片">
          <image-preview :src="form.image" :width="60" :height="60" />
        </t-descriptions-item>
        <t-descriptions-item label="服务类型">{{ form.categoryObj?.name || '' }}</t-descriptions-item>
        <t-descriptions-item label="岗位">{{ form.jobObj?.name || ''}}</t-descriptions-item>
        <t-descriptions-item label="证书">
          <image-preview :src="form.cert" :width="60" :height="60" />
        </t-descriptions-item>
        <t-descriptions-item label="状态">{{ form.status }}</t-descriptions-item>
        <t-descriptions-item label="服务标签">
          <t-space v-if="Array.isArray(form.tag) && form.tag.length > 0" :size="4">
            <t-tag v-for="tag in form.tag" :key="tag" theme="primary" variant="light">{{ tag }}</t-tag>
          </t-space>
          <t-tag v-else-if="form.tag && !Array.isArray(form.tag)" theme="primary" variant="light">{{ form.tag }}</t-tag>
          <span v-else style="color: #999">暂无标签</span>
        </t-descriptions-item>
        <t-descriptions-item label="工作经验" :span="2">{{ form.experience }}</t-descriptions-item>
        <t-descriptions-item label="工作经验详情" :span="2">
          <editor-preview :html-text="form.experienceDetail" />
        </t-descriptions-item>
        <t-descriptions-item label="用户ID">{{ form.userId }}</t-descriptions-item>
        <t-descriptions-item label="认证状态">
          <t-tag v-if="form.auth === 1" theme="success">已认证</t-tag>
          <t-tag v-else theme="warning">未认证</t-tag>
        </t-descriptions-item>
        <t-descriptions-item label="创建时间">{{ parseTime(form.createTime) }}</t-descriptions-item>
      </my-descriptions>
    </t-dialog>
  </t-card>
</template>
<script setup>
defineOptions({
  name: 'Staff',
});
import {
  AddIcon,
  BrowseIcon,
  DeleteIcon,
  DownloadIcon,
  EditIcon,
  RefreshIcon,
  SearchIcon,
  SettingIcon,
} from 'tdesign-icons-vue-next';
import { computed, getCurrentInstance, ref,onMounted } from 'vue';
import { ArrayOps } from '@/utils/array';
import { listStaff, getStaff, delStaff, addStaff, updateStaff, listStaffWithVo } from '@/api/houseKeeping/staff.js';
import Editor from '@/components/editor/index.vue';
import EditorPreview from '@/components/editor-preview/index.vue';
import { listUser } from '@/api/system/user.js'
import { listCategory } from '@/api/houseKeeping/category.js'
import { listJob } from '@/api/houseKeeping/job.ts'


const { proxy } = getCurrentInstance();

const openView = ref(false);
const openViewLoading = ref(false);
const staffRef = ref();
const open = ref(false);
const buttonLoading = ref(false);
const title = ref('');
const staffList = ref([]);
const loading = ref(false);
const columnControllerVisible = ref(false);
const showSearch = ref(true);
const total = ref(0);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const sort = ref();
const userOptions = ref([])
const userList = ref([])
const categoryList = ref([])
const categoryOptions = ref([])
const jobList = ref([])
const jobOptions = ref([])

onMounted(async () => {
  await getUserOptions()
  await getCategoryOptions()
  await getJobOptions()
})



// 获取用户options
const getUserOptions = async () => {
  const response = await listUser({
    pageNum: 1,
    pageSize: 9999
  })
  if(response.code !==200){
    await MessagePlugin.error(response.msg || '获取系统用户失败')
    return
  }
  userList.value = response.rows
  userOptions.value = userList.value.map(item => {
    return {
      label: item.userName,
      value: item.userId
    }
  }) || []
}

// 获取家政服务类型options
const getCategoryOptions = async () => {
  const response = await listCategory({
    pageNum: 1,
    pageSize: 9999
  })
  if(response.code !==200){
    await MessagePlugin.error(response.msg || '获取家政服务类型失败')
    return
  }
  categoryList.value = response.rows
  categoryOptions.value = categoryList.value.map(item => {
    return {
      label: item.name,
      value: item.categoryId
    }
  }) || []
}

// 获取家政服务岗位options
const getJobOptions = async () => {
  const response = await listJob({
    pageNum: 1,
    pageSize: 9999,
    categoryId: form.value.category === '' ? null : form.value.category
  })
  if(response.code !==200){
    await MessagePlugin.error(response.msg || '获取家政服务岗位失败')
    return
  }
  jobList.value = response.rows
  jobOptions.value = jobList.value.map(item => {
    return {
      label: item.name,
      value: item.jobId
    }
  }) || []
}

// 标签变化处理
const handleTagChange = (val, context) => {
  console.log('标签变化:', val, context);
}

// 校验规则
const rules = ref({
  name: [{ max: 255, message: '姓名不能超过255个字符' }],
  title: [{ max: 255, message: '职称不能超过255个字符' }],
  phone: [{ max: 255, message: '联系电话不能超过255个字符' }],
  image: [{ max: 255, message: '照片不能超过255个字符' }],
  category: [{ max: 255, message: '服务类型不能超过255个字符' }],
  job: [{ max: 255, message: '岗位不能超过255个字符' }],
  cert: [{ max: 255, message: '证书不能超过255个字符' }],
  status: [{ max: 50, message: '状态不能超过50个字符' }],
});

// 列显隐信息
const columns = ref([
  { title: `选择列`, colKey: 'row-select', type: 'multiple', width: 50, align: 'center' },
  { title: `姓名`, colKey: 'name', align: 'center' },
  { title: `职称`, colKey: 'title', align: 'center' },
  { title: `性别`, colKey: 'gender', align: 'center' },
  { title: `联系电话`, colKey: 'phone', align: 'center' },
  { title: `年龄`, colKey: 'age', align: 'center' },
  { title: `评分`, colKey: 'star', align: 'center' },
  { title: `照片`, colKey: 'image', align: 'center', width: 100 },
  {
    title: `服务类型`,
    colKey: 'categoryObj',
    align: 'center',
    cell: (h, { row }) => row.categoryObj?.name || ''
  },
  {
    title: `岗位`,
    colKey: 'jobObj',
    align: 'center',
    cell: (h, { row }) => row.jobObj?.name || ''
  },
  { title: `证书`, colKey: 'cert', align: 'center', width: 100 },
  { title: `状态`, colKey: 'status', align: 'center' },
  { title: `服务标签`, colKey: 'tag', align: 'center' },
  { title: `工作经验`, colKey: 'experience', align: 'center', ellipsis: true },
  { title: `工作经验详情`, colKey: 'experienceDetail', align: 'center', ellipsis: true },
  { title: `用户ID`, colKey: 'userId', align: 'center' },
  { title: `认证状态`, colKey: 'auth', align: 'center' },
  { title: `创建时间`, colKey: 'createTime', align: 'center', minWidth: 112, width: 180, sorter: true },
  { title: `操作`, colKey: 'operation', align: 'center', width: 180 },
]);
// 提交表单对象
const form = ref({});
// 查询对象
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  name: undefined,
  title: undefined,
  category: undefined,
  job: undefined,
  status: undefined,
  tag: undefined,
  userId: undefined,
  auth: undefined,
});
// 分页
const pagination = computed(() => {
  return {
    current: queryParams.value.pageNum,
    pageSize: queryParams.value.pageSize,
    total: total.value,
    showJumper: true,
    onChange: (pageInfo) => {
      queryParams.value.pageNum = pageInfo.current;
      queryParams.value.pageSize = pageInfo.pageSize;
      getList();
    },
  };
});

/** 查询服务人员管理列表 */
function getList() {
  loading.value = true;
  listStaffWithVo(queryParams.value)
    .then((response) => {
      staffList.value = response.rows;
      total.value = response.total;
    })
    .finally(() => (loading.value = false));
}

// 表单重置
function reset() {
  form.value = {};
  proxy.resetForm('staffRef');
  form.value.auth = 0
  form.value.gender = 0
  form.value.star = 5
  form.value.tag = []
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm('queryRef');
  queryParams.value.pageNum = 1;
  handleSortChange(null);
}

/** 排序触发事件 */
function handleSortChange(value) {
  sort.value = value;
  if (Array.isArray(value)) {
    queryParams.value.orderByColumn = value.map((item) => item.sortBy).join(',');
    queryParams.value.isAsc = value.map((item) => (item.descending ? 'descending' : 'ascending')).join(',');
  } else {
    queryParams.value.orderByColumn = value?.sortBy;
    queryParams.value.isAsc = value?.descending ? 'descending' : 'ascending';
  }
  getList();
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
  ids.value = selection;
  single.value = selection.length !== 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = '添加服务人员管理';
}

/** 详情按钮操作 */
function handleDetail(row) {
  reset();
  openView.value = true;
  openViewLoading.value = true;
  const staffId = row.staffId;
  getStaff(staffId).then((response) => {
    form.value = response.data;
    console.log('详情页面获取的原始数据:', response.data);
    console.log('原始 tag 值:', form.value.tag, '类型:', typeof form.value.tag);

    // 确保 tag 字段是数组格式
    if (form.value.tag && !Array.isArray(form.value.tag)) {
      try {
        // 尝试解析 JSON 字符串形式的数组
        form.value.tag = JSON.parse(form.value.tag);
        console.log('JSON 解析后的 tag:', form.value.tag);
      } catch (e) {
        // 如果解析失败，按逗号分隔处理
        form.value.tag = form.value.tag.split(',').filter((item) => item.trim());
        console.log('逗号分隔解析后的 tag:', form.value.tag);
      }
    } else if (!form.value.tag) {
      form.value.tag = [];
      console.log('设置为空数组的 tag:', form.value.tag);
    }

    console.log('最终的 tag 值:', form.value.tag);
    openViewLoading.value = false;
  });
}

/** 修改按钮操作 */
function handleUpdate(row) {
  buttonLoading.value = true;
  reset();
  open.value = true;
  title.value = '修改服务人员管理';
  const staffId = row?.staffId || ids.value.at(0);
  getStaff(staffId).then((response) => {
    buttonLoading.value = false;
    form.value = response.data;
    // 确保 tag 字段是数组格式
    if (form.value.tag && !Array.isArray(form.value.tag)) {
      try {
        // 尝试解析 JSON 字符串形式的数组
        form.value.tag = JSON.parse(form.value.tag);
      } catch (e) {
        // 如果解析失败，按逗号分隔处理
        form.value.tag = form.value.tag.split(',').filter((item) => item.trim());
      }
    } else if (!form.value.tag) {
      form.value.tag = [];
    }
  });
}

/** 提交表单 */
function submitForm({ validateResult, firstError }) {
  if (validateResult === true) {
    buttonLoading.value = true;
    const msgLoading = proxy.$modal.msgLoading('提交中...');

    // 深拷贝表单数据
    const submitData = JSON.parse(JSON.stringify(form.value))

    // 处理 tag 字段，将数组转换为字符串形式
    if (Array.isArray(submitData.tag)) {
      submitData.tag = JSON.stringify(submitData.tag);
    }

    if (form.value.staffId) {
      updateStaff(submitData)
        .then(() => {
          proxy.$modal.msgSuccess('修改成功');
          open.value = false;
          getList();
        })
        .finally(() => {
          buttonLoading.value = false;
          proxy.$modal.msgClose(msgLoading);
        });
    } else {
      addStaff(submitData)
        .then(() => {
          proxy.$modal.msgSuccess('新增成功');
          open.value = false;
          getList();
        })
        .finally(() => {
          buttonLoading.value = false;
          proxy.$modal.msgClose(msgLoading);
        });
    }
  } else {
    proxy.$modal.msgError(firstError);
  }
}

/** 删除按钮操作 */
function handleDelete(row) {
  const staffIds = row?.staffId || ids.value;
  proxy.$modal.confirm(`是否确认删除服务人员管理编号为${staffIds}的数据项？`, () => {
    const msgLoading = proxy.$modal.msgLoading('正在删除中...');
    return delStaff(staffIds)
      .then(() => {
        ids.value = ArrayOps.fastDeleteElement(ids.value, staffIds);
        getList();
        proxy.$modal.msgSuccess('删除成功');
      })
      .finally(() => {
        proxy.$modal.msgClose(msgLoading);
      });
  });
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download(
    'houseKeeping/staff/export',
    {
      ...queryParams.value,
    },
    `staff_${new Date().getTime()}.xlsx`,
  );
}

getList();
</script>
