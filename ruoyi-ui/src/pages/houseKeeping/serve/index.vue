<template>
  <t-card>
    <t-space direction="vertical" style="width: 100%">
      <t-form
        v-show="showSearch"
        ref="queryRef"
        :data="queryParams"
        layout="inline"
        reset-type="initial"
        label-width="calc(4em + 12px)"
      >
        <t-form-item label="服务名称" name="name">
          <t-input v-model="queryParams.name" placeholder="请输入服务名称" clearable @enter="handleQuery" />
        </t-form-item>
        <t-form-item label="服务类型" name="category">
          <t-input v-model="queryParams.category" placeholder="请输入服务类型" clearable @enter="handleQuery" />
        </t-form-item>
        <t-form-item label="服务标签" name="tag">
          <t-input v-model="queryParams.tag" placeholder="请输入服务标签" clearable @enter="handleQuery" />
        </t-form-item>
        <t-form-item label-width="0px">
          <t-button theme="primary" @click="handleQuery">
            <template #icon> <search-icon /></template>
            搜索
          </t-button>
          <t-button theme="default" @click="resetQuery">
            <template #icon> <refresh-icon /></template>
            重置
          </t-button>
        </t-form-item>
      </t-form>

      <t-table
        v-model:column-controller-visible="columnControllerVisible"
        :loading="loading"
        hover
        row-key="serveId"
        :data="serveList"
        :columns="columns"
        :selected-row-keys="ids"
        select-on-row-click
        :pagination="pagination"
        :column-controller="{
          hideTriggerButton: true,
        }"
        :sort="sort"
        show-sort-column-bg-color
        @sort-change="handleSortChange"
        @select-change="handleSelectionChange"
      >
        <template #topContent>
          <t-row>
            <t-col flex="auto">
              <t-button v-hasPermi="['houseKeeping:serve:add']" theme="primary" @click="handleAdd">
                <template #icon> <add-icon /></template>
                新增
              </t-button>
              <t-button
                v-hasPermi="['houseKeeping:serve:edit']"
                theme="default"
                variant="outline"
                :disabled="single"
                @click="handleUpdate()"
              >
                <template #icon> <edit-icon /> </template>
                修改
              </t-button>
              <t-button
                v-hasPermi="['houseKeeping:serve:remove']"
                theme="danger"
                variant="outline"
                :disabled="multiple"
                @click="handleDelete()"
              >
                <template #icon> <delete-icon /> </template>
                删除
              </t-button>
              <t-button
                v-hasPermi="['houseKeeping:serve:export']"
                theme="default"
                variant="outline"
                @click="handleExport"
              >
                <template #icon> <download-icon /> </template>
                导出
              </t-button>
              <span class="selected-count">已选 {{ ids.length }} 项</span>
            </t-col>
            <t-col flex="none">
              <t-button theme="default" shape="square" variant="outline" @click="showSearch = !showSearch">
                <template #icon> <search-icon /> </template>
              </t-button>
              <t-button theme="default" variant="outline" @click="columnControllerVisible = true">
                <template #icon> <setting-icon /> </template>
                列配置
              </t-button>
            </t-col>
          </t-row>
        </template>
        <template #image="{ row }">
          <image-preview :src="row.image" :width="60" :height="60" />
        </template>
        <template #operation="{ row }">
          <t-space :size="8" break-line>
            <my-link v-hasPermi="['houseKeeping:serve:query']" @click.stop="handleDetail(row)">
              <template #prefix-icon><browse-icon /></template>详情
            </my-link>
            <my-link v-hasPermi="['houseKeeping:serve:edit']" @click.stop="handleUpdate(row)">
              <template #prefix-icon><edit-icon /></template>修改
            </my-link>
            <my-link v-hasPermi="['houseKeeping:serve:remove']" size="small" @click.stop="handleDelete(row)">
              <template #prefix-icon><delete-icon /></template>删除
            </my-link>
          </t-space>
        </template>
      </t-table>
    </t-space>

    <!-- 添加或修改家政服务管理对话框 -->
    <t-dialog
      v-model:visible="open"
      :header="title"
      destroy-on-close
      :close-on-overlay-click="false"
      width="min(850px, 100%)"
      attach="body"
      :confirm-btn="{
        loading: buttonLoading,
      }"
      @confirm="serveRef.submit()"
    >
      <t-loading :loading="buttonLoading" size="small">
        <t-form
          ref="serveRef"
          label-align="right"
          :data="form"
          :rules="rules"
          label-width="calc(5em + 41px)"
          scroll-to-first-error="smooth"
          @submit="submitForm"
        >
          <t-row :gutter="[24, 24]">
            <t-col :span="6">
              <t-form-item label="服务名称" name="name">
                <t-input v-model="form.name" placeholder="请输入服务名称" clearable />
              </t-form-item>
            </t-col>
            <t-col :span="6">
              <t-form-item label="服务类型" name="category">
                <t-input v-model="form.category" placeholder="请输入服务类型" clearable />
              </t-form-item>
            </t-col>
            <t-col :span="12">
              <t-form-item label="服务标签" name="tag">
                <t-tag-input v-model="form.tag" placeholder="请输入服务标签" clearable @change="handleTagChange" />
              </t-form-item>
            </t-col>
            <t-col :span="6">
              <t-form-item label="图片" name="image">
                <image-upload v-model="form.image" :limit="1" />
              </t-form-item>
            </t-col>
            <t-col :span="6">
              <t-form-item label="服务描述" name="description">
                <t-textarea
                  v-model="form.description"
                  placeholder="请输入服务描述"
                  :autosize="{ minRows: 3, maxRows: 6 }"
                />
              </t-form-item>
            </t-col>
            <t-col :span="12">
              <t-form-item label="服务详细描述" name="descriptionDetail">
                <editor v-model="form.descriptionDetail" />
              </t-form-item>
            </t-col>
            <t-col :span="6">
              <t-form-item label="服务价格" name="price">
                <t-input-number v-model="form.price" placeholder="请输入" />
              </t-form-item>
            </t-col>
            <t-col :span="6">
              <t-form-item label="价格单位" name="unit">
                <t-input v-model="form.unit" placeholder="请输入价格单位" clearable />
              </t-form-item>
            </t-col>
            <t-col :span="6">
              <t-form-item label="服务单位" name="serviceUnit">
                <t-input v-model="form.serviceUnit" placeholder="请输入服务单位" clearable />
              </t-form-item>
            </t-col>
            <t-col :span="6">
              <t-form-item label="状态" name="status">
                <t-radio-group v-model="form.status">
                  <t-radio :value="0">禁用</t-radio>
                  <t-radio :value="1">启用</t-radio>
                </t-radio-group>
              </t-form-item>
            </t-col>
            <t-col :span="6">
              <t-form-item label="是否推送" name="isPush">
                <t-radio-group v-model="form.isPush">
                  <t-radio :value="0">否</t-radio>
                  <t-radio :value="1">是</t-radio>
                </t-radio-group>
              </t-form-item>
            </t-col>
            <t-col :span="6">
              <t-form-item label="浏览次数" name="viewCount">
                <t-input-number v-model="form.viewCount" placeholder="请输入" />
              </t-form-item>
            </t-col>
            <t-col :span="6">
              <t-form-item label="下单次数" name="orderCount">
                <t-input-number v-model="form.orderCount" placeholder="请输入" />
              </t-form-item>
            </t-col>
            <t-col :span="6">
              <t-form-item label="评分" name="star">
                <t-input-number v-model="form.star" placeholder="请输入评分(1-5)" :min="1" :max="5" />
              </t-form-item>
            </t-col>
          </t-row>
        </t-form>
      </t-loading>
    </t-dialog>

    <!-- 家政服务管理详情 -->
    <t-dialog
      v-model:visible="openView"
      header="家政服务管理详情"
      placement="center"
      width="min(700px, 100%)"
      attach="body"
      :footer="false"
    >
      <my-descriptions :loading="openViewLoading">
        <t-descriptions-item label="服务编号">{{ form.serveId }}</t-descriptions-item>
        <t-descriptions-item label="服务名称">{{ form.name }}</t-descriptions-item>
        <t-descriptions-item label="服务类型">{{ form.category }}</t-descriptions-item>
        <t-descriptions-item label="图片">
          <image-preview :src="form.image" :width="60" :height="60" />
        </t-descriptions-item>
        <t-descriptions-item label="服务描述" :span="2">
          {{ form.description }}
        </t-descriptions-item>
        <t-descriptions-item label="服务详细描述" :span="2">
          <editor-preview :html-text="form.descriptionDetail" />
        </t-descriptions-item>
        <t-descriptions-item label="服务价格">{{ form.price }}</t-descriptions-item>
        <t-descriptions-item label="价格单位">{{ form.unit }}</t-descriptions-item>
        <t-descriptions-item label="服务单位">{{ form.serviceUnit }}</t-descriptions-item>
        <t-descriptions-item label="状态">{{ form.status }}</t-descriptions-item>
        <t-descriptions-item label="是否推送">{{ form.isPush }}</t-descriptions-item>
        <t-descriptions-item label="浏览次数">{{ form.viewCount }}</t-descriptions-item>
        <t-descriptions-item label="下单次数">{{ form.orderCount }}</t-descriptions-item>
        <t-descriptions-item label="服务标签">
          <t-space v-if="Array.isArray(form.tag) && form.tag.length > 0" :size="4">
            <t-tag v-for="tag in form.tag" :key="tag" theme="primary" variant="light">{{ tag }}</t-tag>
          </t-space>
          <t-tag v-else-if="form.tag && !Array.isArray(form.tag)" theme="primary" variant="light">{{ form.tag }}</t-tag>
          <span v-else style="color: #999">暂无标签</span>
        </t-descriptions-item>
        <t-descriptions-item label="评分">{{ form.star }}</t-descriptions-item>
        <t-descriptions-item label="创建时间">{{ parseTime(form.createTime) }}</t-descriptions-item>
      </my-descriptions>
    </t-dialog>
  </t-card>
</template>
<script setup>
defineOptions({
  name: 'Serve',
});
import {
  AddIcon,
  BrowseIcon,
  DeleteIcon,
  DownloadIcon,
  EditIcon,
  RefreshIcon,
  SearchIcon,
  SettingIcon,
} from 'tdesign-icons-vue-next';
import { computed, getCurrentInstance, ref } from 'vue';
import { ArrayOps } from '@/utils/array';

import { listServe, getServe, delServe, addServe, updateServe } from '@/api/houseKeeping/serve';
import Editor from '@/components/editor/index.vue';
import EditorPreview from '@/components/editor-preview/index.vue';

const handleTagChange = (val, context) => {
  console.log(val, context);
};

const { proxy } = getCurrentInstance();
const openView = ref(false);
const openViewLoading = ref(false);
const serveRef = ref();
const open = ref(false);
const buttonLoading = ref(false);
const title = ref('');
const serveList = ref([]);
const loading = ref(false);
const columnControllerVisible = ref(false);
const showSearch = ref(true);
const total = ref(0);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const sort = ref();

// 校验规则
const rules = ref({
  name: [{ max: 255, message: '服务名称不能超过255个字符' }],
  category: [{ max: 255, message: '服务类型不能超过255个字符' }],
  image: [{ max: 255, message: '图片不能超过255个字符' }],
  unit: [{ max: 255, message: '价格单位不能超过255个字符' }],
  serviceUnit: [{ max: 255, message: '服务单位不能超过255个字符' }],
});

// 列显隐信息
const columns = ref([
  { title: `选择列`, colKey: 'row-select', type: 'multiple', width: 50, align: 'center' },
  { title: `服务名称`, colKey: 'name', align: 'center' },
  { title: `服务类型`, colKey: 'category', align: 'center' },
  { title: `图片`, colKey: 'image', align: 'center', width: 100 },
  { title: `服务描述`, colKey: 'description', align: 'center' },
  { title: `服务价格`, colKey: 'price', align: 'center' },
  { title: `价格单位`, colKey: 'unit', align: 'center' },
  { title: `服务单位`, colKey: 'serviceUnit', align: 'center' },
  { title: `状态`, colKey: 'status', align: 'center' },
  { title: `是否推送`, colKey: 'isPush', align: 'center' },
  { title: `浏览次数`, colKey: 'viewCount', align: 'center' },
  { title: `下单次数`, colKey: 'orderCount', align: 'center' },
  { title: `服务标签`, colKey: 'tag', align: 'center' },
  { title: `评分`, colKey: 'star', align: 'center' },
  { title: `创建时间`, colKey: 'createTime', align: 'center', minWidth: 112, width: 180, sorter: true },
  { title: `操作`, colKey: 'operation', align: 'center', width: 180 },
]);
// 提交表单对象
const form = ref({});
// 查询对象
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  name: undefined,
  category: undefined,
  tag: undefined,
});
// 分页
const pagination = computed(() => {
  return {
    current: queryParams.value.pageNum,
    pageSize: queryParams.value.pageSize,
    total: total.value,
    showJumper: true,
    onChange: (pageInfo) => {
      queryParams.value.pageNum = pageInfo.current;
      queryParams.value.pageSize = pageInfo.pageSize;
      getList();
    },
  };
});

/** 查询家政服务管理列表 */
function getList() {
  loading.value = true;
  listServe(queryParams.value)
    .then((response) => {
      serveList.value = response.rows;
      total.value = response.total;
    })
    .finally(() => (loading.value = false));
}

// 表单重置
function reset() {
  form.value = {};
  proxy.resetForm('serveRef');
  form.value.status = 1;
  form.value.isPush = 0;
  form.value.descriptionDetail = '';
  form.value.tag = []; // 初始化为空数组
  form.value.star = 5; // 初始化评分为5
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm('queryRef');
  queryParams.value.pageNum = 1;
  handleSortChange(null);
}

/** 排序触发事件 */
function handleSortChange(value) {
  sort.value = value;
  if (Array.isArray(value)) {
    queryParams.value.orderByColumn = value.map((item) => item.sortBy).join(',');
    queryParams.value.isAsc = value.map((item) => (item.descending ? 'descending' : 'ascending')).join(',');
  } else {
    queryParams.value.orderByColumn = value?.sortBy;
    queryParams.value.isAsc = value?.descending ? 'descending' : 'ascending';
  }
  getList();
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
  ids.value = selection;
  single.value = selection.length !== 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = '添加家政服务管理';
}

/** 详情按钮操作 */
function handleDetail(row) {
  reset();
  openView.value = true;
  openViewLoading.value = true;
  const serveId = row.serveId;
  getServe(serveId).then((response) => {
    form.value = response.data;
    console.log('详情页面获取的原始数据:', response.data);
    console.log('原始 tag 值:', form.value.tag, '类型:', typeof form.value.tag);

    // 确保 tag 字段是数组格式
    if (form.value.tag && !Array.isArray(form.value.tag)) {
      try {
        // 尝试解析 JSON 字符串形式的数组
        form.value.tag = JSON.parse(form.value.tag);
        console.log('JSON 解析后的 tag:', form.value.tag);
      } catch (e) {
        // 如果解析失败，按逗号分隔处理
        form.value.tag = form.value.tag.split(',').filter((item) => item.trim());
        console.log('逗号分隔解析后的 tag:', form.value.tag);
      }
    } else if (!form.value.tag) {
      form.value.tag = [];
      console.log('设置为空数组的 tag:', form.value.tag);
    }

    console.log('最终的 tag 值:', form.value.tag);
    openViewLoading.value = false;
  });
}

/** 修改按钮操作 */
function handleUpdate(row) {
  buttonLoading.value = true;
  reset();
  open.value = true;
  title.value = '修改家政服务管理';
  const serveId = row?.serveId || ids.value.at(0);
  getServe(serveId).then((response) => {
    buttonLoading.value = false;
    form.value = response.data;
    // 确保 tag 字段是数组格式
    if (form.value.tag && !Array.isArray(form.value.tag)) {
      try {
        // 尝试解析 JSON 字符串形式的数组
        form.value.tag = JSON.parse(form.value.tag);
      } catch (e) {
        // 如果解析失败，按逗号分隔处理
        form.value.tag = form.value.tag.split(',').filter((item) => item.trim());
      }
    } else if (!form.value.tag) {
      form.value.tag = [];
    }
  });
}

/** 提交表单 */
function submitForm({ validateResult, firstError }) {
  console.log('提交的表单的值为', form.value);

  if (validateResult === true) {
    buttonLoading.value = true;
    const msgLoading = proxy.$modal.msgLoading('提交中...');

    // 深拷贝表单数据
    const submitData = JSON.parse(JSON.stringify(form.value));

    // 处理 tag 字段，将数组转换为字符串形式
    if (Array.isArray(submitData.tag)) {
      submitData.tag = JSON.stringify(submitData.tag);
    }

    if (form.value.serveId) {
      updateServe(submitData)
        .then(() => {
          proxy.$modal.msgSuccess('修改成功');
          open.value = false;
          getList();
        })
        .finally(() => {
          buttonLoading.value = false;
          proxy.$modal.msgClose(msgLoading);
        });
    } else {
      addServe(submitData)
        .then(() => {
          proxy.$modal.msgSuccess('新增成功');
          open.value = false;
          getList();
        })
        .finally(() => {
          buttonLoading.value = false;
          proxy.$modal.msgClose(msgLoading);
        });
    }
  } else {
    proxy.$modal.msgError(firstError);
  }
}

/** 删除按钮操作 */
function handleDelete(row) {
  const serveIds = row?.serveId || ids.value;
  proxy.$modal.confirm(`是否确认删除家政服务管理编号为${serveIds}的数据项？`, () => {
    const msgLoading = proxy.$modal.msgLoading('正在删除中...');
    return delServe(serveIds)
      .then(() => {
        ids.value = ArrayOps.fastDeleteElement(ids.value, serveIds);
        getList();
        proxy.$modal.msgSuccess('删除成功');
      })
      .finally(() => {
        proxy.$modal.msgClose(msgLoading);
      });
  });
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download(
    'houseKeeping/serve/export',
    {
      ...queryParams.value,
    },
    `serve_${new Date().getTime()}.xlsx`,
  );
}

getList();
</script>
