<template>
  <t-card>
    <t-space direction="vertical" style="width: 100%">
      <t-form
        v-show="showSearch"
        ref="queryRef"
        :data="queryParams"
        layout="inline"
        reset-type="initial"
        label-width="calc(4em + 12px)"
      >
        <t-form-item label="服务编号" name="serveId">
          <t-input v-model="queryParams.serveId" placeholder="请输入服务编号" clearable @enter="handleQuery" />
        </t-form-item>
        <t-form-item label="联系人" name="contactName">
          <t-input v-model="queryParams.contactName" placeholder="请输入联系人" clearable @enter="handleQuery" />
        </t-form-item>
        <t-form-item label="服务人员编号" name="staffId">
          <t-input v-model="queryParams.staffId" placeholder="请输入服务人员编号" clearable @enter="handleQuery" />
        </t-form-item>
        <t-form-item label="顾客编号" name="customerId">
          <t-input v-model="queryParams.customerId" placeholder="请输入顾客编号" clearable @enter="handleQuery" />
        </t-form-item>
        <t-form-item label-width="0px">
          <t-button theme="primary" @click="handleQuery">
            <template #icon> <search-icon /></template>
            搜索
          </t-button>
          <t-button theme="default" @click="resetQuery">
            <template #icon> <refresh-icon /></template>
            重置
          </t-button>
        </t-form-item>
      </t-form>

      <t-table
        v-model:column-controller-visible="columnControllerVisible"
        :loading="loading"
        hover
        row-key="reservationId"
        :data="reservationList"
        :columns="columns"
        :selected-row-keys="ids"
        select-on-row-click
        :pagination="pagination"
        :column-controller="{
          hideTriggerButton: true,
        }"
        :sort="sort"
        show-sort-column-bg-color
        @sort-change="handleSortChange"
        @select-change="handleSelectionChange"
      >
        <template #topContent>
          <t-row>
            <t-col flex="auto">
              <t-button v-hasPermi="['houseKeeping:reservation:add']" theme="primary" @click="handleAdd">
                <template #icon> <add-icon /></template>
                新增
              </t-button>
              <t-button
                v-hasPermi="['houseKeeping:reservation:edit']"
                theme="default"
                variant="outline"
                :disabled="single"
                @click="handleUpdate()"
              >
                <template #icon> <edit-icon /> </template>
                修改
              </t-button>
              <t-button
                v-hasPermi="['houseKeeping:reservation:remove']"
                theme="danger"
                variant="outline"
                :disabled="multiple"
                @click="handleDelete()"
              >
                <template #icon> <delete-icon /> </template>
                删除
              </t-button>
              <t-button
                v-hasPermi="['houseKeeping:reservation:export']"
                theme="default"
                variant="outline"
                @click="handleExport"
              >
                <template #icon> <download-icon /> </template>
                导出
              </t-button>
              <span class="selected-count">已选 {{ ids.length }} 项</span>
            </t-col>
            <t-col flex="none">
              <t-button theme="default" shape="square" variant="outline" @click="showSearch = !showSearch">
                <template #icon> <search-icon /> </template>
              </t-button>
              <t-button theme="default" variant="outline" @click="columnControllerVisible = true">
                <template #icon> <setting-icon /> </template>
                列配置
              </t-button>
            </t-col>
          </t-row>
        </template>
        <template #operation="{ row }">
          <t-space :size="8" break-line>
            <my-link v-hasPermi="['houseKeeping:reservation:query']" @click.stop="handleDetail(row)">
              <template #prefix-icon><browse-icon /></template>详情
            </my-link>
            <my-link v-hasPermi="['houseKeeping:reservation:edit']" @click.stop="handleUpdate(row)">
              <template #prefix-icon><edit-icon /></template>修改
            </my-link>
            <my-link v-hasPermi="['houseKeeping:reservation:remove']" size="small" @click.stop="handleDelete(row)">
              <template #prefix-icon><delete-icon /></template>删除
            </my-link>
          </t-space>
        </template>
      </t-table>
    </t-space>

    <!-- 添加或修改预约管理对话框 -->
    <t-dialog
      v-model:visible="open"
      :header="title"
      destroy-on-close
      :close-on-overlay-click="false"
      width="min(850px, 100%)"
      attach="body"
      :confirm-btn="{
        loading: buttonLoading,
      }"
      @confirm="reservationRef.submit()"
    >
      <t-loading :loading="buttonLoading" size="small">
        <t-form
          ref="reservationRef"
          label-align="right"
          :data="form"
          :rules="rules"
          label-width="calc(5em + 41px)"
          scroll-to-first-error="smooth"
          @submit="submitForm"
        >
          <t-row :gutter="[24, 24]">
            <t-col :span="6">
              <t-form-item label="服务" name="serveId">
                <t-select v-model="form.serveId" :options="serveOptions" placeholder="请选择服务" clearable></t-select>
              </t-form-item>
            </t-col>
            <t-col :span="6">
              <t-form-item label="联系人" name="contactName">
                <t-input v-model="form.contactName" placeholder="请输入联系人" clearable />
              </t-form-item>
            </t-col>
            <t-col :span="6">
              <t-form-item label="服务日期" name="serviceDate">
                <t-date-picker
                  v-model="form.serviceDate"
                  allow-input
                  clearable
                  placeholder="请选择服务日期"
                  format="YYYY-MM-DD"
                  style="width: 100%"
                  :disable-date="disableDate"
                />
              </t-form-item>
            </t-col>
            <t-col :span="6">
              <t-form-item label="服务时间段" name="serviceTime">
                <t-select
                  v-model="form.serviceTime"
                  :options="serviceTimeOptions"
                  placeholder="请选择服务时间段"
                  clearable
                ></t-select>
              </t-form-item>
            </t-col>
            <t-col :span="6">
              <t-form-item label="电话" name="phone">
                <t-input v-model="form.phone" placeholder="请输入电话" clearable />
              </t-form-item>
            </t-col>
            <t-col :span="6">
              <t-form-item label="地址" name="address">
                <t-input v-model="form.address" placeholder="请输入地址" clearable />
              </t-form-item>
            </t-col>
            <t-col :span="12">
              <t-form-item label="特殊要求" name="special">
                <t-textarea v-model="form.special" placeholder="请输入特殊要求" />
              </t-form-item>
            </t-col>
            <t-col :span="6">
              <t-form-item label="顾客" name="customerId">
                <t-select
                  v-model="form.customerId"
                  :options="userOptions"
                  placeholder="请选择顾客"
                  clearable
                ></t-select>
              </t-form-item>
            </t-col>
            <t-col :span="6">
              <t-form-item label="状态" name="status">
                <t-select
                  v-model="form.status"
                  :options="statusOptions"
                  placeholder="请选择预约状态"
                  clearable
                ></t-select>
              </t-form-item>
            </t-col>
            <t-col :span="6">
              <t-form-item label="服务人员" name="staffId">
                <t-select
                  v-model="form.staffId"
                  :options="staffOptions"
                  placeholder="请选择服务人员"
                  clearable
                ></t-select>
              </t-form-item>
            </t-col>

            <t-col :span="12">
              <t-form-item label="取消原因" name="cancelReason">
                <t-textarea v-model="form.cancelReason" placeholder="请输入取消原因" />
              </t-form-item>
            </t-col>
          </t-row>
        </t-form>
      </t-loading>
    </t-dialog>

    <!-- 预约管理详情 -->
    <t-dialog
      v-model:visible="openView"
      header="预约管理详情"
      placement="center"
      width="min(700px, 100%)"
      attach="body"
      :footer="false"
    >
      <my-descriptions :loading="openViewLoading">
        <t-descriptions-item label="预约编号">{{ form.reservationId }}</t-descriptions-item>
        <t-descriptions-item label="服务名称">{{ form.serve?.name }}</t-descriptions-item>
        <t-descriptions-item label="服务日期">{{ parseTime(form.serviceDate, '{y}-{m}-{d}') }}</t-descriptions-item>
          <t-descriptions-item label="服务时间段">{{ form.serviceTime }}</t-descriptions-item>
        <t-descriptions-item label="联系人">{{ form.contactName }}</t-descriptions-item>
        <t-descriptions-item label="电话">{{ form.phone }}</t-descriptions-item>
        <t-descriptions-item label="地址" :span="2">{{ form.address }}</t-descriptions-item>
        <t-descriptions-item label="特殊要求" :span="2">{{ form.special }}</t-descriptions-item>
        <t-descriptions-item label="状态">{{ form.status }}</t-descriptions-item>
        <t-descriptions-item label="服务人员">{{ form.staff?.name  || '暂无' }}</t-descriptions-item>
        <t-descriptions-item label="顾客">{{
          form.customer?.userName || '暂无'
        }}</t-descriptions-item>
        <t-descriptions-item label="创建时间">{{ parseTime(form.createTime) }}</t-descriptions-item>
        <t-descriptions-item label="取消原因" :span="2">{{ form.cancelReason }}</t-descriptions-item>
        <t-descriptions-item label="更新时间">{{ parseTime(form.updateTime) }}</t-descriptions-item>
        <t-descriptions-item label="更新人">{{ form.updateBy }}</t-descriptions-item>
      </my-descriptions>
    </t-dialog>
  </t-card>
</template>
<script setup>
defineOptions({
  name: 'Reservation',
});
import {
  AddIcon,
  BrowseIcon,
  DeleteIcon,
  DownloadIcon,
  EditIcon,
  RefreshIcon,
  SearchIcon,
  SettingIcon,
} from 'tdesign-icons-vue-next';
import { computed, getCurrentInstance, ref, onMounted } from 'vue';
import { ArrayOps } from '@/utils/array';
import { parseTime } from '@/utils/ruoyi';

import {
  listReservation,
  getReservation,
  delReservation,
  addReservation,
  updateReservation,
} from '@/api/houseKeeping/reservation';
import { listServe } from '@/api/houseKeeping/serve.js';
import { listUser } from '@/api/system/user.js';
import { listStaff } from '@/api/houseKeeping/staff.js';

const { proxy } = getCurrentInstance();

const openView = ref(false);
const openViewLoading = ref(false);
const reservationRef = ref();
const open = ref(false);
const buttonLoading = ref(false);
const title = ref('');
const reservationList = ref([]);
const loading = ref(false);
const columnControllerVisible = ref(false);
const showSearch = ref(true);
const total = ref(0);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const sort = ref();
const serveList = ref([]);
const serveOptions = ref([]);
const userList = ref([]);
const userOptions = ref([]);
const staffOptions = ref([]);

onMounted(async () => {
  getList();
  await getServeOptions();
  await getUserOptions();
  await getStaffOptions();
});
const getServeOptions = async () => {
  const response = await listServe({
    pageNum: 1,
    pageSize: 9999,
  });
  if (response.code !== 200) {
    await MessagePlugin.error(response.msg || '加载服务列表失败');
    return;
  }
  serveList.value = response.rows;
  serveOptions.value = response.rows.map((item) => {
    return {
      label: item.name,
      value: item.serveId,
    };
  });
};

const getUserOptions = async () => {
  const response = await listUser({
    pageNum: 1,
    pageSize: 9999,
  });
  if (response.code !== 200) {
    await MessagePlugin.error(response.msg || '加载用户列表失败');
    return;
  }
  userList.value = response.rows;
  userOptions.value = response.rows.map((item) => {
    return {
      label: item.userName,
      value: item.userId,
    };
  });
};

const getStaffOptions = async () => {
  const response = await listStaff({
    pageNum: 1,
    pageSize: 9999,
    auth: 1,
  });
  if (response.code !== 200) {
    await MessagePlugin.error(response.msg || '加载服务人员列表失败');
    return;
  }
  staffOptions.value = response.rows.map((item) => {
    return {
      label: item.name,
      value: item.staffId,
    };
  });
};
// 服务时间段列表
const serviceTimeOptions = ref([
  { label: '08:00-10:00', value: '08:00-10:00' },
  { label: '10:00-12:00', value: '10:00-12:00' },
  { label: '12:00-14:00', value: '12:00-14:00' },
  { label: '14:00-16:00', value: '14:00-16:00' },
  { label: '16:00-18:00', value: '16:00-18:00' },
  { label: '18:00-20:00', value: '18:00-20:00' },
  { label: '20:00-22:00', value: '20:00-22:00' },
]);

const statusOptions = ref([
  { label: '待支付', value: '待支付' },
  { label: '已支付', value: '已支付' },
  { label: '已取消', value: '已取消' },
  { label: '待服务', value: '待服务' },
  { label: '服务中', value: '服务中' },
  { label: '已完成', value: '已完成' },
]);

// 校验规则
const rules = ref({
  serveId: [{ max: 255, message: '服务编号不能超过255个字符' }],
  contactName: [{ max: 255, message: '联系人不能超过255个字符' }],
  serviceTime: [{ max: 255, message: '服务时间段不能超过255个字符' }],
  phone: [{ max: 255, message: '电话不能超过255个字符' }],
  address: [{ max: 255, message: '地址不能超过255个字符' }],
  status: [{ max: 255, message: '状态不能超过255个字符' }],
  staffId: [{ max: 255, message: '服务人员编号不能超过255个字符' }],
  customerId: [{ max: 255, message: '顾客编号不能超过255个字符' }],
});

// 列显隐信息
const columns = ref([
  { title: `选择列`, colKey: 'row-select', type: 'multiple', width: 50, align: 'center' },
  { title: `联系人`, colKey: 'contactName', align: 'center' },
  {
    title: `服务日期`,
    colKey: 'serviceDate',
    align: 'center',
    minWidth: 112,
    width: 170,
    cell: (h, { row }) => parseTime(row.serviceDate, '{y}-{m}-{d}'),
  },

  { title: `服务时间段`, colKey: 'serviceTime', align: 'center', width: 170 },
  { title: `电话`, colKey: 'phone', align: 'center' },
  { title: `地址`, colKey: 'address', align: 'center' },
  { title: `特殊要求`, colKey: 'special', align: 'center', ellipsis: true },
  { title: `状态`, colKey: 'status', align: 'center' },
  {
    title: `服务人员`,
    colKey: 'staff',
    align: 'center',
    cell: (h, { row }) => row.staff?.name || '暂无',
  },
  {
    title: `顾客`,
    colKey: 'customer',
    align: 'center',
    cell: (h, { row }) => row.customer?.userName || '暂无',
  },
  {
    title: `取消原因`,
    colKey: 'cancelReason',
    align: 'center',
    ellipsis: true,
    cell: (h, { row }) => row.cancelReason || '无',
  },
  { title: `创建时间`, colKey: 'createTime', align: 'center', minWidth: 112, width: 180, sorter: true },
  { title: `更新时间`, colKey: 'updateTime', align: 'center', minWidth: 112, width: 180 },
  { title: `更新人`, colKey: 'updateBy', align: 'center' },
  { title: `操作`, colKey: 'operation', align: 'center', width: 180 },
]);
// 提交表单对象
const form = ref({});
// 查询对象
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  serveId: undefined,
  contactName: undefined,
  staffId: undefined,
  customerId: undefined,
});
// 分页
const pagination = computed(() => {
  return {
    current: queryParams.value.pageNum,
    pageSize: queryParams.value.pageSize,
    total: total.value,
    showJumper: true,
    onChange: (pageInfo) => {
      queryParams.value.pageNum = pageInfo.current;
      queryParams.value.pageSize = pageInfo.pageSize;
      getList();
    },
  };
});

// 禁用过去的日期
const disableDate = (date) => {
  const today = new Date();
  today.setHours(0, 0, 0, 0);
  return date < today;
};

/** 查询预约管理列表 */
function getList() {
  loading.value = true;
  listReservation(queryParams.value)
    .then((response) => {
      reservationList.value = response.rows;
      total.value = response.total;
    })
    .finally(() => (loading.value = false));
}

// 表单重置
function reset() {
  form.value = {};
  proxy.resetForm('reservationRef');
  form.value.status = '待支付';
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm('queryRef');
  queryParams.value.pageNum = 1;
  handleSortChange(null);
}

/** 排序触发事件 */
function handleSortChange(value) {
  sort.value = value;
  if (Array.isArray(value)) {
    queryParams.value.orderByColumn = value.map((item) => item.sortBy).join(',');
    queryParams.value.isAsc = value.map((item) => (item.descending ? 'descending' : 'ascending')).join(',');
  } else {
    queryParams.value.orderByColumn = value?.sortBy;
    queryParams.value.isAsc = value?.descending ? 'descending' : 'ascending';
  }
  getList();
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
  ids.value = selection;
  single.value = selection.length !== 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = '添加预约管理';
}

/** 详情按钮操作 */
function handleDetail(row) {
  reset();
  openView.value = true;
  openViewLoading.value = true;
  const reservationId = row.reservationId;
  getReservation(reservationId).then((response) => {
    form.value = response.data;
    openViewLoading.value = false;
  });
}

/** 修改按钮操作 */
function handleUpdate(row) {
  buttonLoading.value = true;
  reset();
  open.value = true;
  title.value = '修改预约管理';
  const reservationId = row?.reservationId || ids.value.at(0);
  getReservation(reservationId).then((response) => {
    buttonLoading.value = false;
    form.value = response.data;
  });
}

/** 提交表单 */
function submitForm({ validateResult, firstError }) {
  if (validateResult === true) {
    buttonLoading.value = true;
    const msgLoading = proxy.$modal.msgLoading('提交中...');
    if (form.value.reservationId) {
      updateReservation(form.value)
        .then(() => {
          proxy.$modal.msgSuccess('修改成功');
          open.value = false;
          getList();
        })
        .finally(() => {
          buttonLoading.value = false;
          proxy.$modal.msgClose(msgLoading);
        });
    } else {
      addReservation(form.value)
        .then(() => {
          proxy.$modal.msgSuccess('新增成功');
          open.value = false;
          getList();
        })
        .finally(() => {
          buttonLoading.value = false;
          proxy.$modal.msgClose(msgLoading);
        });
    }
  } else {
    proxy.$modal.msgError(firstError);
  }
}

/** 删除按钮操作 */
function handleDelete(row) {
  const reservationIds = row?.reservationId || ids.value;
  proxy.$modal.confirm(`是否确认删除预约管理编号为${reservationIds}的数据项？`, () => {
    const msgLoading = proxy.$modal.msgLoading('正在删除中...');
    return delReservation(reservationIds)
      .then(() => {
        ids.value = ArrayOps.fastDeleteElement(ids.value, reservationIds);
        getList();
        proxy.$modal.msgSuccess('删除成功');
      })
      .finally(() => {
        proxy.$modal.msgClose(msgLoading);
      });
  });
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download(
    'houseKeeping/reservation/export',
    {
      ...queryParams.value,
    },
    `reservation_${new Date().getTime()}.xlsx`,
  );
}
</script>
