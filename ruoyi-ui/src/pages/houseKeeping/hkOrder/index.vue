<template>
  <t-card>
    <t-space direction="vertical" style="width: 100%">
      <t-form
        v-show="showSearch"
        ref="queryRef"
        :data="queryParams"
        layout="inline"
        reset-type="initial"
        label-width="calc(4em + 12px)"
      >
        <t-form-item label="预约编号" name="reservationId">
          <t-input v-model="queryParams.reservationId" placeholder="请输入预约编号" clearable @enter="handleQuery" />
        </t-form-item>
        <t-form-item label="顾客编号" name="customerId">
          <t-input v-model="queryParams.customerId" placeholder="请输入顾客编号" clearable @enter="handleQuery" />
        </t-form-item>
        <t-form-item label="服务编号" name="serveId">
          <t-input v-model="queryParams.serveId" placeholder="请输入服务编号" clearable @enter="handleQuery" />
        </t-form-item>
        <t-form-item label="服务人员编号" name="staffId">
          <t-input v-model="queryParams.staffId" placeholder="请输入服务人员编号" clearable @enter="handleQuery" />
        </t-form-item>
        <t-form-item label-width="0px">
          <t-button theme="primary" @click="handleQuery">
            <template #icon> <search-icon /></template>
            搜索
          </t-button>
          <t-button theme="default" @click="resetQuery">
            <template #icon> <refresh-icon /></template>
            重置
          </t-button>
        </t-form-item>
      </t-form>

      <t-table
        v-model:column-controller-visible="columnControllerVisible"
        :loading="loading"
        hover
        row-key="orderId"
        :data="hkOrderList"
        :columns="columns"
        :selected-row-keys="ids"
        select-on-row-click
        :pagination="pagination"
        :column-controller="{
          hideTriggerButton: true,
        }"
        :sort="sort"
        show-sort-column-bg-color
        @sort-change="handleSortChange"
        @select-change="handleSelectionChange"
      >
        <template #topContent>
          <t-row>
            <t-col flex="auto">
              <t-button v-hasPermi="['houseKeeping:hkOrder:add']" theme="primary" @click="handleAdd">
                <template #icon> <add-icon /></template>
                新增
              </t-button>
              <t-button
                v-hasPermi="['houseKeeping:hkOrder:edit']"
                theme="default"
                variant="outline"
                :disabled="single"
                @click="handleUpdate()"
              >
                <template #icon> <edit-icon /> </template>
                修改
              </t-button>
              <t-button
                v-hasPermi="['houseKeeping:hkOrder:remove']"
                theme="danger"
                variant="outline"
                :disabled="multiple"
                @click="handleDelete()"
              >
                <template #icon> <delete-icon /> </template>
                删除
              </t-button>
              <t-button
                v-hasPermi="['houseKeeping:hkOrder:export']"
                theme="default"
                variant="outline"
                @click="handleExport"
              >
                <template #icon> <download-icon /> </template>
                导出
              </t-button>
              <span class="selected-count">已选 {{ ids.length }} 项</span>
            </t-col>
            <t-col flex="none">
              <t-button theme="default" shape="square" variant="outline" @click="showSearch = !showSearch">
                <template #icon> <search-icon /> </template>
              </t-button>
              <t-button theme="default" variant="outline" @click="columnControllerVisible = true">
                <template #icon> <setting-icon /> </template>
                列配置
              </t-button>
            </t-col>
          </t-row>
        </template>
        <template #status="{ row }">
          <t-tag
            :theme="getStatusTheme(row.status)"
            variant="light"
          >
            {{ row.status || '-' }}
          </t-tag>
        </template>
        <template #operation="{ row }">
          <t-space :size="8" break-line>
            <my-link v-hasPermi="['houseKeeping:hkOrder:query']" @click.stop="handleDetail(row)">
              <template #prefix-icon><browse-icon /></template>详情
            </my-link>
            <my-link v-hasPermi="['houseKeeping:hkOrder:edit']" @click.stop="handleUpdate(row)">
              <template #prefix-icon><edit-icon /></template>修改
            </my-link>
            <my-link v-hasPermi="['houseKeeping:hkOrder:remove']" size="small" @click.stop="handleDelete(row)">
              <template #prefix-icon><delete-icon /></template>删除
            </my-link>
          </t-space>
        </template>
      </t-table>
    </t-space>

    <!-- 添加或修改支付订单对话框 -->
    <t-dialog
      v-model:visible="open"
      :header="title"
      destroy-on-close
      :close-on-overlay-click="false"
      width="min(750px, 100%)"
      attach="body"
      :confirm-btn="{
        loading: buttonLoading,
      }"
      @confirm="hkOrderRef.submit()"
    >
      <t-loading :loading="buttonLoading" size="small">
        <t-form
          ref="hkOrderRef"
          label-align="right"
          :data="form"
          :rules="rules"
          label-width="calc(5em + 41px)"
          scroll-to-first-error="smooth"
          @submit="submitForm"
        >
          <t-row :gutter="[24, 24]">
            <t-col :span="6">
              <t-form-item label="预约编号" name="reservationId">
                <t-input v-model="form.reservationId" placeholder="请输入预约编号" clearable />
              </t-form-item>
            </t-col>
            <t-col :span="6">
              <t-form-item label="顾客编号" name="customerId">
                <t-input v-model="form.customerId" placeholder="请输入顾客编号" clearable />
              </t-form-item>
            </t-col>
            <t-col :span="6">
              <t-form-item label="服务编号" name="serveId">
                <t-input v-model="form.serveId" placeholder="请输入服务编号" clearable />
              </t-form-item>
            </t-col>
            <t-col :span="6">
              <t-form-item label="服务人员编号" name="staffId">
                <t-input v-model="form.staffId" placeholder="请输入服务人员编号" clearable />
              </t-form-item>
            </t-col>
            <t-col :span="6">
              <t-form-item label="订单金额" name="orderAmount">
                <t-input-number v-model="form.orderAmount" placeholder="请输入" />
              </t-form-item>
            </t-col>
            <t-col :span="6">
              <t-form-item label="订单状态" name="status">
                <t-radio-group v-model="form.status">
                  <t-select
                    v-model="form.status"
                    :options="statusOptions"
                    placeholder="请选择订单状态"
                    clearable
                  ></t-select>
                </t-radio-group>
              </t-form-item>
            </t-col>
          </t-row>
        </t-form>
      </t-loading>
    </t-dialog>

    <!-- 支付订单详情 -->
    <t-dialog
      v-model:visible="openView"
      header="支付订单详情"
      placement="center"
      width="min(700px, 100%)"
      attach="body"
      :footer="false"
    >
      <my-descriptions :loading="openViewLoading">
        <t-descriptions-item label="订单编号">{{ form.orderId }}</t-descriptions-item>
        <t-descriptions-item label="预约编号">{{ form.reservationId }}</t-descriptions-item>
        <t-descriptions-item label="顾客名称">{{ form.customer?.nickName || form.customer?.userName || '-' }}</t-descriptions-item>
        <t-descriptions-item label="服务编号">{{ form.serveId }}</t-descriptions-item>
        <t-descriptions-item label="服务人员编号">{{ form.staffId }}</t-descriptions-item>
        <t-descriptions-item label="订单金额">{{ form.orderAmount }}</t-descriptions-item>
        <t-descriptions-item label="订单状态">
          <t-tag
            :theme="getStatusTheme(form.status)"
            variant="light"
          >
            {{ form.status || '-' }}
          </t-tag>
        </t-descriptions-item>
        <t-descriptions-item label="创建时间">{{ parseTime(form.createTime) }}</t-descriptions-item>
      </my-descriptions>
    </t-dialog>
  </t-card>
</template>
<script setup>
defineOptions({
  name: 'HkOrder',
});
import {
  AddIcon,
  BrowseIcon,
  DeleteIcon,
  DownloadIcon,
  EditIcon,
  RefreshIcon,
  SearchIcon,
  SettingIcon,
} from 'tdesign-icons-vue-next';
import { computed, getCurrentInstance, ref } from 'vue';
import { ArrayOps } from '@/utils/array.js';
import { parseTime } from '@/utils/ruoyi.js';

import { listHkOrder, getHkOrder, delHkOrder, addHkOrder, updateHkOrder } from '@/api/houseKeeping/hkOrder.js';

const { proxy } = getCurrentInstance();

const openView = ref(false);
const openViewLoading = ref(false);
const hkOrderRef = ref();
const open = ref(false);
const buttonLoading = ref(false);
const title = ref('');
const hkOrderList = ref([]);
const loading = ref(false);
const columnControllerVisible = ref(false);
const showSearch = ref(true);
const total = ref(0);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const sort = ref();

// 校验规则
const rules = ref({
  reservationId: [{ max: 32, message: '预约编号不能超过32个字符' }],
  customerId: [{ max: 32, message: '顾客编号不能超过32个字符' }],
  serveId: [{ max: 32, message: '服务编号不能超过32个字符' }],
  staffId: [{ max: 32, message: '服务人员编号不能超过32个字符' }],
  status: [{ max: 20, message: '订单状态不能超过20个字符' }],
});

const statusOptions = ref([
  { label: '待支付', value: '待支付' },
  { label: '已支付', value: '已支付' },
  { label: "已取消", value: '已取消' },
  { label: "已完成", value: '已完成' },
  { label: "退款中", value: '退款中' },
  { label: "已退款", value: '已退款' },
]);

// 列显隐信息
const columns = ref([
  { title: `选择列`, colKey: 'row-select', type: 'multiple', width: 50, align: 'center' },
  { title: `预约编号`, colKey: 'reservationId', align: 'center' },
  {
    title: `顾客名称`,
    colKey: 'customer',
    align: 'center',
    cell: (h, { row }) => row.customer?.userName || '-',
  },
  {
    title: `服务名称`,
    colKey: 'serve',
    align: 'center',
    cell: (h, { row }) => row.serve?.name || '-',
  },
  {
    title: `服务人员`,
    colKey: 'staff',
    align: 'center',
    cell: (h, { row }) => row.staff?.name || '-',
  },
  { title: `订单金额`, colKey: 'orderAmount', align: 'center' },
  { title: `订单状态`, colKey: 'status', align: 'center' },
  { title: `创建时间`, colKey: 'createTime', align: 'center', minWidth: 112, width: 180, sorter: true },
  { title: `操作`, colKey: 'operation', align: 'center', width: 180 },
]);
// 提交表单对象
const form = ref({});
// 查询对象
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  reservationId: undefined,
  customerId: undefined,
  serveId: undefined,
  staffId: undefined,
});
// 分页
const pagination = computed(() => {
  return {
    current: queryParams.value.pageNum,
    pageSize: queryParams.value.pageSize,
    total: total.value,
    showJumper: true,
    onChange: (pageInfo) => {
      queryParams.value.pageNum = pageInfo.current;
      queryParams.value.pageSize = pageInfo.pageSize;
      getList();
    },
  };
});

/** 查询支付订单列表 */
function getList() {
  loading.value = true;
  listHkOrder(queryParams.value)
    .then((response) => {
      hkOrderList.value = response.rows;
      total.value = response.total;
    })
    .finally(() => (loading.value = false));
}

// 表单重置
function reset() {
  form.value = {};
  proxy.resetForm('hkOrderRef');
  form.value.status = '待支付';
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm('queryRef');
  queryParams.value.pageNum = 1;
  handleSortChange(null);
}

/** 排序触发事件 */
function handleSortChange(value) {
  sort.value = value;
  if (Array.isArray(value)) {
    queryParams.value.orderByColumn = value.map((item) => item.sortBy).join(',');
    queryParams.value.isAsc = value.map((item) => (item.descending ? 'descending' : 'ascending')).join(',');
  } else {
    queryParams.value.orderByColumn = value?.sortBy;
    queryParams.value.isAsc = value?.descending ? 'descending' : 'ascending';
  }
  getList();
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
  ids.value = selection;
  single.value = selection.length !== 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = '添加支付订单';
}

/** 详情按钮操作 */
function handleDetail(row) {
  reset();
  openView.value = true;
  openViewLoading.value = true;
  const orderId = row.orderId;
  getHkOrder(orderId).then((response) => {
    form.value = response.data;
    openViewLoading.value = false;
  });
}

/** 修改按钮操作 */
function handleUpdate(row) {
  buttonLoading.value = true;
  reset();
  open.value = true;
  title.value = '修改支付订单';
  const orderId = row?.orderId || ids.value.at(0);
  getHkOrder(orderId).then((response) => {
    buttonLoading.value = false;
    form.value = response.data;
  });
}

/** 提交表单 */
function submitForm({ validateResult, firstError }) {
  if (validateResult === true) {
    buttonLoading.value = true;
    const msgLoading = proxy.$modal.msgLoading('提交中...');
    if (form.value.orderId) {
      updateHkOrder(form.value)
        .then(() => {
          proxy.$modal.msgSuccess('修改成功');
          open.value = false;
          getList();
        })
        .finally(() => {
          buttonLoading.value = false;
          proxy.$modal.msgClose(msgLoading);
        });
    } else {
      addHkOrder(form.value)
        .then(() => {
          proxy.$modal.msgSuccess('新增成功');
          open.value = false;
          getList();
        })
        .finally(() => {
          buttonLoading.value = false;
          proxy.$modal.msgClose(msgLoading);
        });
    }
  } else {
    proxy.$modal.msgError(firstError);
  }
}

/** 删除按钮操作 */
function handleDelete(row) {
  const orderIds = row?.orderId || ids.value;
  proxy.$modal.confirm(`是否确认删除支付订单编号为${orderIds}的数据项？`, () => {
    const msgLoading = proxy.$modal.msgLoading('正在删除中...');
    return delHkOrder(orderIds)
      .then(() => {
        ids.value = ArrayOps.fastDeleteElement(ids.value, orderIds);
        getList();
        proxy.$modal.msgSuccess('删除成功');
      })
      .finally(() => {
        proxy.$modal.msgClose(msgLoading);
      });
  });
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download(
    'houseKeeping/hkOrder/export',
    {
      ...queryParams.value,
    },
    `hkOrder_${new Date().getTime()}.xlsx`,
  );
}

/** 获取状态主题色 */
function getStatusTheme(status) {
  const statusMap = {
    '待支付': 'warning',
    '已支付': 'success',
    '已取消': 'danger',
    '已完成': 'primary',
    '退款中': 'default',
    '已退款': 'default'
  };
  return statusMap[status] || 'default';
}

getList();
</script>
