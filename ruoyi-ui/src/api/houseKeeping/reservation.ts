import type { R, TableDataInfo } from '@/api/model/resultModel';
import type { ReservationForm, ReservationQuery, ReservationVo } from '@/api/houseKeeping/model/reservationModel';
import { request } from '@/utils/request';

/**
 * 查询预约管理列表
 * @param query 查询参数
 */
export function listReservation(query?: ReservationQuery) {
  return request.get<TableDataInfo<ReservationVo>>({
    url: '/houseKeeping/reservation/list',
    params: query,
  });
}

/**
 * 查询预约管理详细
 * @param reservationId 主键
 */
export function getReservation(reservationId: string) {
  return request.get<R<ReservationVo>>({
    url: `/houseKeeping/reservation/${reservationId}`,
  });
}

/**
 * 新增预约管理
 * @param data 表单数据
 */
export function addReservation(data: ReservationForm) {
  return request.post<R<void>>({
    url: '/houseKeeping/reservation',
    data,
  });
}

/**
 * 修改预约管理
 * @param data
 */
export function updateReservation(data: ReservationForm) {
  return request.put<R<void>>({
    url: '/houseKeeping/reservation',
    data,
  });
}

/**
 * 删除预约管理
 * @param reservationIds 主键串
 */
export function delReservation(reservationIds: string | Array<string>) {
  return request.delete<R<void>>({
    url: `/houseKeeping/reservation/${reservationIds}`,
  });
}
