import { request } from '@/utils/request';

/**
 * 查询家政服务管理列表
 * @param query 查询参数
 */
export function listServe(query) {
  return request.get({
    url: '/houseKeeping/serve/list',
    params: query,
  });
}

/**
 * 查询家政服务管理详细
 * @param serveId 主键
 */
export function getServe(serveId) {
  return request.get({
    url: `/houseKeeping/serve/${serveId}`,
  });
}

/**
 * 新增家政服务管理
 * @param data 表单数据
 */
export function addServe(data) {
  return request.post({
    url: '/houseKeeping/serve',
    data,
  });
}

/**
 * 修改家政服务管理
 * @param data
 */
export function updateServe(data) {
  return request.put({
    url: '/houseKeeping/serve',
    data,
  });
}

/**
 * 删除家政服务管理
 * @param serveIds 主键串
 */
export function delServe(serveIds) {
  return request.delete({
    url: `/houseKeeping/serve/${serveIds}`,
  });
}
