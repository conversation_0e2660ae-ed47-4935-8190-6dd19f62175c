import type { R, TableDataInfo } from '@/api/model/resultModel';
import type { CategoryForm, CategoryQuery, CategoryVo } from '@/api/houseKeeping/model/categoryModel';
import { request } from '@/utils/request';

/**
 * 查询服务类型管理列表
 * @param query 查询参数
 */
export function listCategory(query?: CategoryQuery) {
  return request.get<TableDataInfo<CategoryVo>>({
    url: '/houseKeeping/category/list',
    params: query,
  });
}

/**
 * 查询服务类型管理详细
 * @param categoryId 主键
 */
export function getCategory(categoryId: string) {
  return request.get<R<CategoryVo>>({
    url: `/houseKeeping/category/${categoryId}`,
  });
}

/**
 * 新增服务类型管理
 * @param data 表单数据
 */
export function addCategory(data: CategoryForm) {
  return request.post<R<void>>({
    url: '/houseKeeping/category',
    data,
  });
}

/**
 * 修改服务类型管理
 * @param data
 */
export function updateCategory(data: CategoryForm) {
  return request.put<R<void>>({
    url: '/houseKeeping/category',
    data,
  });
}

/**
 * 删除服务类型管理
 * @param categoryIds 主键串
 */
export function delCategory(categoryIds: string | Array<string>) {
  return request.delete<R<void>>({
    url: `/houseKeeping/category/${categoryIds}`,
  });
}
