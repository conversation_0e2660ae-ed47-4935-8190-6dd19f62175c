import type { R, TableDataInfo } from '@/api/model/resultModel';
import type { JobForm, JobQuery, JobVo } from '@/api/houseKeeping/model/jobModel';
import { request } from '@/utils/request';

/**
 * 查询岗位管理列表
 * @param query 查询参数
 */
export function listJob(query?: JobQuery) {
  return request.get<TableDataInfo<JobVo>>({
    url: '/houseKeeping/job/list',
    params: query,
  });
}

/**
 * 查询岗位管理详细
 * @param jobId 主键
 */
export function getJob(jobId: string) {
  return request.get<R<JobVo>>({
    url: `/houseKeeping/job/${jobId}`,
  });
}

/**
 * 新增岗位管理
 * @param data 表单数据
 */
export function addJob(data: JobForm) {
  return request.post<R<void>>({
    url: '/houseKeeping/job',
    data,
  });
}

/**
 * 修改岗位管理
 * @param data
 */
export function updateJob(data: JobForm) {
  return request.put<R<void>>({
    url: '/houseKeeping/job',
    data,
  });
}

/**
 * 删除岗位管理
 * @param jobIds 主键串
 */
export function delJob(jobIds: string | Array<string>) {
  return request.delete<R<void>>({
    url: `/houseKeeping/job/${jobIds}`,
  });
}
