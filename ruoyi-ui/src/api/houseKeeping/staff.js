import { request } from '@/utils/request';

/**
 * 查询服务人员管理列表
 * @param query 查询参数
 */
export function listStaff(query) {
  return request.get({
    url: '/houseKeeping/staff/list',
    params: query,
  });
}

/**
 * 查询服务人员管理列表
 * @param query 查询参数
 */
export function listStaffWithVo(query) {
  return request.get({
    url: '/houseKeeping/staff/listStaff',
    params: query,
  });
}



/**
 * 查询服务人员管理详细
 * @param staffId 主键
 */
export function getStaff(staffId) {
  return request.get({
    url: `/houseKeeping/staff/${staffId}`,
  });
}

/**
 * 新增服务人员管理
 * @param data 表单数据
 */
export function addStaff(data) {
  return request.post({
    url: '/houseKeeping/staff',
    data,
  });
}

/**
 * 修改服务人员管理
 * @param data
 */
export function updateStaff(data) {
  return request.put({
    url: '/houseKeeping/staff',
    data,
  });
}

/**
 * 删除服务人员管理
 * @param staffIds 主键串
 */
export function delStaff(staffIds) {
  return request.delete({
    url: `/houseKeeping/staff/${staffIds}`,
  });
}
