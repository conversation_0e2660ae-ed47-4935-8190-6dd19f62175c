import type { BaseEntity } from '@/api/model/resultModel';

/**
 * 服务类型管理查询对象
 */
export interface CategoryQuery extends BaseEntity {
  /** 服务名称 */
  name?: string;
}
/**
 * 服务类型管理业务对象
 */
export interface CategoryForm {
  /** 服务类型编号 */
  categoryId?: string;
  /** 服务名称 */
  name?: string;
  /** 服务描述 */
  description?: string;
  /** 排序 */
  sort?: number;
  /** 状态 */
  status?: number;
}
/**
 * 服务类型管理视图对象
 */
export interface CategoryVo {
  /** 服务类型编号 */
  categoryId?: string;
  /** 服务名称 */
  name?: string;
  /** 服务描述 */
  description?: string;
  /** 排序 */
  sort?: number;
  /** 状态 */
  status?: number;
  /** 创建时间 */
  createTime?: any;
}
