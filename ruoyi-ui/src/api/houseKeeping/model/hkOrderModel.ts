import type { BaseEntity } from '@/api/model/resultModel';

/**
 * 支付订单查询对象
 */
export interface HkOrderQuery extends BaseEntity {
  /** 预约编号 */
  reservationId?: string;
  /** 顾客编号 */
  customerId?: string;
  /** 服务编号 */
  serveId?: string;
  /** 服务人员编号 */
  staffId?: string;
}
/**
 * 支付订单业务对象
 */
export interface HkOrderForm {
  /** 订单编号 */
  orderId?: string;
  /** 预约编号 */
  reservationId?: string;
  /** 顾客编号 */
  customerId?: string;
  /** 服务编号 */
  serveId?: string;
  /** 服务人员编号 */
  staffId?: string;
  /** 订单金额 */
  orderAmount?: number;
  /** 订单状态 */
  status?: string;
}
/**
 * 支付订单视图对象
 */
export interface HkOrderVo {
  /** 订单编号 */
  orderId?: string;
  /** 预约编号 */
  reservationId?: string;
  /** 顾客编号 */
  customerId?: string;
  /** 服务编号 */
  serveId?: string;
  /** 服务人员编号 */
  staffId?: string;
  /** 订单金额 */
  orderAmount?: number;
  /** 订单状态 */
  status?: string;
  /** 创建时间 */
  createTime?: any;
}
