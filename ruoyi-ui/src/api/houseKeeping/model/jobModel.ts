import type { BaseEntity } from '@/api/model/resultModel';

/**
 * 岗位管理查询对象
 */
export interface JobQuery extends BaseEntity {
  /** 服务类型 */
  categoryId?: string;
  /** 岗位名称 */
  name?: string;
}
/**
 * 岗位管理业务对象
 */
export interface JobForm {
  /** 岗位编号 */
  jobId?: string;
  /** 服务类型 */
  categoryId?: string;
  /** 岗位名称 */
  name?: string;
  /** 岗位描述 */
  description?: string;
  /** 图片 */
  image?: string;
  /** 岗位要求 */
  requirements?: string;
  /** 状态 */
  status?: number;
  /** 排序 */
  sort?: number;
}
/**
 * 岗位管理视图对象
 */
export interface JobVo {
  /** 岗位编号 */
  jobId?: string;
  /** 服务类型 */
  categoryId?: string;
  /** 岗位名称 */
  name?: string;
  /** 岗位描述 */
  description?: string;
  /** 图片 */
  image?: string;
  /** 岗位要求 */
  requirements?: string;
  /** 状态 */
  status?: number;
  /** 排序 */
  sort?: number;
  /** 创建时间 */
  createTime?: any;
}
