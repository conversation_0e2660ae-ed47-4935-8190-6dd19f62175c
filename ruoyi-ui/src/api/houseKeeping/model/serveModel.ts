import type { BaseEntity } from '@/api/model/resultModel';

/**
 * 家政服务管理查询对象
 */
export interface ServeQuery extends BaseEntity {
  /** 服务名称 */
  name?: string;
  /** 服务类型 */
  category?: string;
  /** 服务标签 */
  tag?: string;
}
/**
 * 家政服务管理业务对象
 */
export interface ServeForm {
  /** 服务编号 */
  serveId?: string;
  /** 服务名称 */
  name?: string;
  /** 服务类型 */
  category?: string;
  /** 图片 */
  image?: string;
  /** 服务描述 */
  description?: string;
  /** 服务详细描述 */
  descriptionDetail?: string;
  /** 服务价格 */
  price?: number;
  /** 价格单位 */
  unit?: string;
  /** 服务单位 */
  serviceUnit?: string;
  /** 状态 */
  status?: number;
  /** 是否推送 */
  isPush?: number;
  /** 浏览次数 */
  viewCount?: number;
  /** 下单次数 */
  orderCount?: number;
  /** 服务标签 */
  tag?: string;
  /** 评分 */
  star?: number;
}
/**
 * 家政服务管理视图对象
 */
export interface ServeVo {
  /** 服务编号 */
  serveId?: string;
  /** 服务名称 */
  name?: string;
  /** 服务类型 */
  category?: string;
  /** 图片 */
  image?: string;
  /** 服务描述 */
  description?: string;
  /** 服务详细描述 */
  descriptionDetail?: string;
  /** 服务价格 */
  price?: number;
  /** 价格单位 */
  unit?: string;
  /** 服务单位 */
  serviceUnit?: string;
  /** 状态 */
  status?: number;
  /** 是否推送 */
  isPush?: number;
  /** 浏览次数 */
  viewCount?: number;
  /** 下单次数 */
  orderCount?: number;
  /** 服务标签 */
  tag?: string;
  /** 评分 */
  star?: number;
  /** 创建时间 */
  createTime?: any;
}
