import type { BaseEntity } from '@/api/model/resultModel';

/**
 * 预约管理查询对象
 */
export interface ReservationQuery extends BaseEntity {
  /** 服务编号 */
  serveId?: string;
  /** 联系人 */
  contactName?: string;
  /** 服务人员编号 */
  staffId?: string;
  /** 顾客编号 */
  customerId?: string;
}
/**
 * 预约管理业务对象
 */
export interface ReservationForm {
  /** 预约编号 */
  reservationId?: string;
  /** 服务编号 */
  serveId?: string;
  /** 服务日期 */
  serviceDate?: any;
  /** 联系人 */
  contactName?: string;
  /** 服务时间段 */
  serviceTime?: string;
  /** 电话 */
  phone?: string;
  /** 地址 */
  address?: string;
  /** 特殊要求 */
  special?: string;
  /** 状态 */
  status?: string;
  /** 服务人员编号 */
  staffId?: string;
  /** 顾客编号 */
  customerId?: string;
  /** 取消原因 */
  cancelReason?: string;
}
/**
 * 服务人员对象
 */
export interface Staff {
  /** 职员编号 */
  staffId?: string;
  /** 姓名 */
  name?: string;
  /** 职称 */
  title?: string;
  /** 性别 */
  gender?: number;
  /** 联系电话 */
  phone?: string;
  /** 年龄 */
  age?: number;
  /** 评分 */
  star?: number;
  /** 照片 */
  image?: string;
  /** 服务类型 */
  category?: string;
  /** 岗位 */
  job?: string;
  /** 证书 */
  cert?: string;
  /** 状态 */
  status?: string;
  /** 服务标签 */
  tag?: string;
  /** 工作经验 */
  experience?: string;
  /** 工作经验详情 */
  experienceDetail?: string;
  /** 用户ID */
  userId?: string;
  /** 认证状态 */
  auth?: number;
  /** 创建时间 */
  createTime?: any;
}

/**
 * 服务对象
 */
export interface Serve {
  /** 服务编号 */
  serveId?: string;
  /** 服务名称 */
  name?: string;
  /** 服务类型 */
  category?: string;
  /** 服务图片 */
  image?: string;
  /** 服务描述 */
  description?: string;
  /** 服务详细描述 */
  descriptionDetail?: string;
  /** 价格 */
  price?: number;
  /** 单位 */
  unit?: string;
  /** 服务单位 */
  serviceUnit?: string;
  /** 状态 */
  status?: string;
  /** 是否推荐 */
  isPush?: string;
  /** 浏览次数 */
  viewCount?: number;
  /** 订单次数 */
  orderCount?: number;
  /** 服务标签 */
  tag?: string;
  /** 评分 */
  star?: number;
  /** 创建时间 */
  createTime?: any;
}

/**
 * 用户对象
 */
export interface SysUser {
  /** 用户ID */
  userId?: string;
  /** 用户名 */
  userName?: string;
  /** 昵称 */
  nickName?: string;
  /** 用户类型 */
  userType?: string;
  /** 邮箱 */
  email?: string;
  /** 手机号 */
  phonenumber?: string;
  /** 性别 */
  sex?: string;
  /** 头像 */
  avatar?: string;
  /** 状态 */
  status?: string;
  /** 创建时间 */
  createTime?: any;
}

/**
 * 预约管理视图对象
 */
export interface ReservationVo {
  /** 预约编号 */
  reservationId?: string;
  /** 服务编号 */
  serveId?: string;
  /** 服务对象 */
  serve?: Serve;
  /** 服务日期 */
  serviceDate?: any;
  /** 联系人 */
  contactName?: string;
  /** 服务时间段 */
  serviceTime?: string;
  /** 电话 */
  phone?: string;
  /** 地址 */
  address?: string;
  /** 特殊要求 */
  special?: string;
  /** 状态 */
  status?: string;
  /** 服务人员编号 */
  staffId?: string;
  /** 服务人员对象 */
  staff?: Staff;
  /** 顾客编号 */
  customerId?: string;
  /** 顾客对象 */
  customer?: SysUser;
  /** 创建时间 */
  createTime?: any;
  /** 取消原因 */
  cancelReason?: string;
  /** 更新时间 */
  updateTime?: any;
  /** 更新人编号 */
  updateBy?: string;
}
