import type { BaseEntity } from '@/api/model/resultModel';

/**
 * 服务人员管理查询对象
 */
export interface StaffQuery extends BaseEntity {
  /** 姓名 */
  name?: string;
  /** 职称 */
  title?: string;
  /** 服务类型 */
  category?: string;
  /** 岗位 */
  job?: string;
  /** 状态 */
  status?: string;
  /** 服务标签 */
  tag?: string;
  /** 用户ID */
  userId?: string;
  /** 认证状态 */
  auth?: number;
}
/**
 * 服务人员管理业务对象
 */
export interface StaffForm {
  /** 职员编号 */
  staffId?: string;
  /** 姓名 */
  name?: string;
  /** 职称 */
  title?: string;
  /** 性别 */
  gender?: number;
  /** 联系电话 */
  phone?: string;
  /** 年龄 */
  age?: number;
  /** 评分 */
  star?: number;
  /** 照片 */
  image?: string;
  /** 服务类型 */
  category?: string;
  /** 岗位 */
  job?: string;
  /** 证书 */
  cert?: string;
  /** 状态 */
  status?: string;
  /** 服务标签 */
  tag?: string;
  /** 工作经验 */
  experience?: string;
  /** 工作经验详情 */
  experienceDetail?: string;
  /** 用户ID */
  userId?: string;
  /** 认证状态 */
  auth?: number;
}
/**
 * 服务人员管理视图对象
 */
export interface StaffVo {
  /** 职员编号 */
  staffId?: string;
  /** 姓名 */
  name?: string;
  /** 职称 */
  title?: string;
  /** 性别 */
  gender?: number;
  /** 联系电话 */
  phone?: string;
  /** 年龄 */
  age?: number;
  /** 评分 */
  star?: number;
  /** 照片 */
  image?: string;
  /** 服务类型 */
  category?: string;
  /** 岗位 */
  job?: string;
  /** 证书 */
  cert?: string;
  /** 状态 */
  status?: string;
  /** 服务标签 */
  tag?: string;
  /** 工作经验 */
  experience?: string;
  /** 工作经验详情 */
  experienceDetail?: string;
  /** 用户ID */
  userId?: string;
  /** 认证状态 */
  auth?: number;
  /** 创建时间 */
  createTime?: any;
}
