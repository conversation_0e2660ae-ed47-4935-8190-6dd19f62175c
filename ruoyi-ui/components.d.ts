/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
// biome-ignore lint: disable
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    ApprovalRecord: typeof import('./src/components/Process/approvalRecord.vue')['default']
    Color: typeof import('./src/components/color/index.vue')['default']
    DeptTree: typeof import('./src/components/dept-tree/index.vue')['default']
    DictTag: typeof import('./src/components/dict-tag/index.vue')['default']
    Editor: typeof import('./src/components/editor/index.vue')['default']
    EditorPreview: typeof import('./src/components/editor-preview/index.vue')['default']
    FileUpload: typeof import('./src/components/file-upload/index.vue')['default']
    HtmlImagePreview: typeof import('./src/components/html-image-preview/index.vue')['default']
    IconSelect: typeof import('./src/components/icon-select/index.vue')['default']
    IFrame: typeof import('./src/components/i-frame/index.vue')['default']
    ImagePreview: typeof import('./src/components/image-preview/index.vue')['default']
    ImageUpload: typeof import('./src/components/image-upload/index.vue')['default']
    MyDescriptions: typeof import('./src/components/my-descriptions/index.vue')['default']
    MyLink: typeof import('./src/components/my-link/index.vue')['default']
    MyScrollbar: typeof import('./src/components/my-scrollbar/index.vue')['default']
    ParentView: typeof import('./src/components/ParentView/index.vue')['default']
    PreviewCode: typeof import('./src/components/preview-code/index.vue')['default']
    ProcessMeddle: typeof import('./src/components/Process/processMeddle.vue')['default']
    ProductCard: typeof import('./src/components/product-card/index.vue')['default']
    RectSelect: typeof import('./src/components/rect-select/index.vue')['default']
    Result: typeof import('./src/components/result/index.vue')['default']
    RIcon: typeof import('./src/components/r-icon/index.vue')['default']
    RoleSelect: typeof import('./src/components/role-select/index.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    SubmitVerify: typeof import('./src/components/Process/submitVerify.vue')['default']
    TAside: typeof import('tdesign-vue-next')['Aside']
    TAutoComplete: typeof import('tdesign-vue-next')['AutoComplete']
    TAvatar: typeof import('tdesign-vue-next')['Avatar']
    TAvatarGroup: typeof import('tdesign-vue-next')['AvatarGroup']
    TBadge: typeof import('tdesign-vue-next')['Badge']
    TBreadcrumb: typeof import('tdesign-vue-next')['Breadcrumb']
    TBreadcrumbItem: typeof import('tdesign-vue-next')['BreadcrumbItem']
    TButton: typeof import('tdesign-vue-next')['Button']
    TCard: typeof import('tdesign-vue-next')['Card']
    TCheckbox: typeof import('tdesign-vue-next')['Checkbox']
    TCheckboxGroup: typeof import('tdesign-vue-next')['CheckboxGroup']
    TCol: typeof import('tdesign-vue-next')['Col']
    TColorPickerPanel: typeof import('tdesign-vue-next')['ColorPickerPanel']
    TConfigProvider: typeof import('tdesign-vue-next')['ConfigProvider']
    TContent: typeof import('tdesign-vue-next')['Content']
    TDatePicker: typeof import('tdesign-vue-next')['DatePicker']
    TDateRangePicker: typeof import('tdesign-vue-next')['DateRangePicker']
    TDescriptions: typeof import('tdesign-vue-next')['Descriptions']
    TDescriptionsItem: typeof import('tdesign-vue-next')['DescriptionsItem']
    TDialog: typeof import('tdesign-vue-next')['Dialog']
    TDivider: typeof import('tdesign-vue-next')['Divider']
    TDrawer: typeof import('tdesign-vue-next')['Drawer']
    TDropdown: typeof import('tdesign-vue-next')['Dropdown']
    TDropdownItem: typeof import('tdesign-vue-next')['DropdownItem']
    TDropdownMenu: typeof import('tdesign-vue-next')['DropdownMenu']
    TEnhancedTable: typeof import('tdesign-vue-next')['EnhancedTable']
    TFooter: typeof import('tdesign-vue-next')['Footer']
    TForm: typeof import('tdesign-vue-next')['Form']
    TFormItem: typeof import('tdesign-vue-next')['FormItem']
    THeader: typeof import('tdesign-vue-next')['Header']
    THeadMenu: typeof import('tdesign-vue-next')['HeadMenu']
    Thumbnail: typeof import('./src/components/thumbnail/index.vue')['default']
    TImage: typeof import('tdesign-vue-next')['Image']
    TImageViewer: typeof import('tdesign-vue-next')['ImageViewer']
    TInput: typeof import('tdesign-vue-next')['Input']
    TInputAdornment: typeof import('tdesign-vue-next')['InputAdornment']
    TInputNumber: typeof import('tdesign-vue-next')['InputNumber']
    TLayout: typeof import('tdesign-vue-next')['Layout']
    TLink: typeof import('tdesign-vue-next')['Link']
    TList: typeof import('tdesign-vue-next')['List']
    TListItem: typeof import('tdesign-vue-next')['ListItem']
    TLoading: typeof import('tdesign-vue-next')['Loading']
    TMenu: typeof import('tdesign-vue-next')['Menu']
    TMenuItem: typeof import('tdesign-vue-next')['MenuItem']
    ToggleAdvanced: typeof import('./src/components/toggle-advanced/index.vue')['default']
    TOption: typeof import('tdesign-vue-next')['Option']
    TPagination: typeof import('tdesign-vue-next')['Pagination']
    TPaginationMini: typeof import('tdesign-vue-next')['PaginationMini']
    TPopup: typeof import('tdesign-vue-next')['Popup']
    TRadio: typeof import('tdesign-vue-next')['Radio']
    TRadioButton: typeof import('tdesign-vue-next')['RadioButton']
    TRadioGroup: typeof import('tdesign-vue-next')['RadioGroup']
    TRangeInput: typeof import('tdesign-vue-next')['RangeInput']
    TranslateSelect: typeof import('./src/components/translate-select/index.vue')['default']
    Trend: typeof import('./src/components/trend/index.vue')['default']
    TRow: typeof import('tdesign-vue-next')['Row']
    TSelect: typeof import('tdesign-vue-next')['Select']
    TSkeleton: typeof import('tdesign-vue-next')['Skeleton']
    TSpace: typeof import('tdesign-vue-next')['Space']
    TSubmenu: typeof import('tdesign-vue-next')['Submenu']
    TSwitch: typeof import('tdesign-vue-next')['Switch']
    TTable: typeof import('tdesign-vue-next')['Table']
    TTabPanel: typeof import('tdesign-vue-next')['TabPanel']
    TTabs: typeof import('tdesign-vue-next')['Tabs']
    TTag: typeof import('tdesign-vue-next')['Tag']
    TTagInput: typeof import('tdesign-vue-next')['TagInput']
    TTextarea: typeof import('tdesign-vue-next')['Textarea']
    TTimeline: typeof import('tdesign-vue-next')['Timeline']
    TTimelineItem: typeof import('tdesign-vue-next')['TimelineItem']
    TTooltip: typeof import('tdesign-vue-next')['Tooltip']
    TTree: typeof import('tdesign-vue-next')['Tree']
    TTreeSelect: typeof import('tdesign-vue-next')['TreeSelect']
    TUpload: typeof import('tdesign-vue-next')['Upload']
    TWatermark: typeof import('tdesign-vue-next')['Watermark']
    UploadExcel: typeof import('./src/components/upload-excel/index.vue')['default']
    UploadSelect: typeof import('./src/components/upload-select/index.vue')['default']
    UserSelect: typeof import('./src/components/user-select/index.vue')['default']
  }
}
