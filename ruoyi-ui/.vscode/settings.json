{"files.eol": "\n", "editor.tabSize": 2, "[vue]": {"editor.formatOnSave": true, "editor.defaultFormatter": "esbenp.prettier-vscode"}, "[typescriptreact]": {"editor.formatOnSave": true, "editor.defaultFormatter": "esbenp.prettier-vscode"}, "[javascriptreact]": {"editor.formatOnSave": true, "editor.defaultFormatter": "esbenp.prettier-vscode"}, "[typescript]": {"editor.formatOnSave": true, "editor.defaultFormatter": "esbenp.prettier-vscode"}, "[javascript]": {"editor.formatOnSave": true, "editor.defaultFormatter": "esbenp.prettier-vscode"}, "cSpell.words": ["tdesign", "tvision", "echarts", "nprogress", "commitlint", "stylelint", "pinia", "qrcode"]}