# sys_user表mp_open_id字段集成测试

## 测试步骤

### 1. 数据库字段添加
执行以下SQL语句添加mp_open_id字段：
```sql
ALTER TABLE sys_user ADD COLUMN mp_open_id varchar(100) DEFAULT NULL COMMENT '微信小程序openId';
CREATE INDEX idx_sys_user_mp_open_id ON sys_user(mp_open_id);
```

### 2. 后端功能测试

#### 2.1 新增用户测试
- 测试新增用户时mp_open_id字段是否正常保存
- 测试mp_open_id唯一性校验是否生效

#### 2.2 修改用户测试
- 测试修改用户时mp_open_id字段是否正常更新
- 测试修改时mp_open_id唯一性校验是否生效

#### 2.3 查询用户测试
- 测试根据mp_open_id查询用户功能
- 测试用户列表查询是否包含mp_open_id字段
- 测试用户详情查询是否包含mp_open_id字段

#### 2.4 导入导出测试
- 测试用户导出是否包含mp_open_id字段
- 测试用户导入是否支持mp_open_id字段

### 3. API接口测试

#### 3.1 新增用户接口
```
POST /system/user
{
  "userName": "testuser",
  "nickName": "测试用户",
  "deptId": 103,
  "mpOpenId": "wx_test_openid_123",
  "roleIds": [2],
  "postIds": [1]
}
```

#### 3.2 修改用户接口
```
PUT /system/user
{
  "userId": 1,
  "userName": "testuser",
  "nickName": "测试用户",
  "deptId": 103,
  "mpOpenId": "wx_test_openid_456",
  "roleIds": [2],
  "postIds": [1]
}
```

#### 3.3 查询用户接口
```
GET /system/user/list?mpOpenId=wx_test_openid_123
```

### 4. 前端功能测试
- 测试用户管理页面是否显示mp_open_id字段
- 测试新增/编辑用户表单是否包含mp_open_id输入框
- 测试用户列表是否显示mp_open_id列

## 预期结果
1. 数据库字段添加成功，索引创建成功
2. 后端所有相关类都包含mp_open_id字段
3. API接口能正常处理mp_open_id字段
4. 唯一性校验正常工作
5. 查询功能正常工作
6. 导入导出功能正常工作

## 注意事项
1. mp_open_id字段允许为空，但如果有值则必须唯一
2. 查询时支持精确匹配mp_open_id
3. 导入导出时mp_open_id字段可选
