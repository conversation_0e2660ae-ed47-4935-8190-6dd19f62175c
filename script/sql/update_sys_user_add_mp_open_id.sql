-- ----------------------------
-- 为sys_user表添加mp_open_id字段
-- ----------------------------

-- MySQL版本
ALTER TABLE sys_user ADD COLUMN mp_open_id varchar(100) DEFAULT NULL COMMENT '微信小程序openId';

-- 创建索引以提高查询性能
CREATE INDEX idx_sys_user_mp_open_id ON sys_user(mp_open_id);

-- PostgreSQL版本（如果使用PostgreSQL）
-- ALTER TABLE sys_user ADD COLUMN mp_open_id varchar(100) DEFAULT NULL;
-- COMMENT ON COLUMN sys_user.mp_open_id IS '微信小程序openId';
-- CREATE INDEX idx_sys_user_mp_open_id ON sys_user(mp_open_id);

-- Oracle版本（如果使用Oracle）
-- ALTER TABLE sys_user ADD mp_open_id varchar2(100) DEFAULT NULL;
-- COMMENT ON COLUMN sys_user.mp_open_id IS '微信小程序openId';
-- CREATE INDEX idx_sys_user_mp_open_id ON sys_user(mp_open_id);
