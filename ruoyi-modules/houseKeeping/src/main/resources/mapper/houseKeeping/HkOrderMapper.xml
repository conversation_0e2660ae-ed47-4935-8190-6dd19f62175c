<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.houseKeeping.mapper.HkOrderMapper">

    <resultMap id="HkOrderResult" autoMapping="true" type="org.dromara.houseKeeping.domain.HkOrder">
        <id property="orderId" column="order_id"/>
    </resultMap>
    <resultMap id="HkOrderResultVo" autoMapping="true" type="org.dromara.houseKeeping.domain.vo.HkOrderVo">
        <id property="orderId" column="order_id"/>
    </resultMap>

    <sql id="selectHkOrderVo">
        select ho.order_id, ho.reservation_id, ho.customer_id, ho.serve_id, ho.staff_id, ho.order_amount, ho.status, ho.create_time from hk_order ho
    </sql>
    <select id="queryList" parameterType="org.dromara.houseKeeping.domain.query.HkOrderQuery" resultMap="HkOrderResultVo">
        <include refid="selectHkOrderVo"/>
        <where>
            <if test="reservationId != null and reservationId != ''"> and ho.reservation_id like concat(concat('%', #{reservationId}), '%')</if>
            <if test="customerId != null and customerId != ''"> and ho.customer_id like concat(concat('%', #{customerId}), '%')</if>
            <if test="serveId != null and serveId != ''"> and ho.serve_id like concat(concat('%', #{serveId}), '%')</if>
            <if test="staffId != null and staffId != ''"> and ho.staff_id like concat(concat('%', #{staffId}), '%')</if>
        </where>
    </select>

</mapper>
