<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.houseKeeping.mapper.HkOrderMapper">

    <resultMap id="HkOrderResult" autoMapping="true" type="org.dromara.houseKeeping.domain.HkOrder">
        <id property="orderId" column="order_id"/>
    </resultMap>
    <resultMap id="HkOrderResultVo" autoMapping="true" type="org.dromara.houseKeeping.domain.vo.HkOrderVo">
        <id property="orderId" column="order_id"/>
        <!-- 关联预约对象 -->
        <association property="reservation" javaType="org.dromara.houseKeeping.domain.Reservation" autoMapping="true" columnPrefix="reservation_"/>
        <!-- 关联顾客对象 -->
        <association property="customer" javaType="org.dromara.system.domain.SysUser" autoMapping="true" columnPrefix="customer_"/>
        <!-- 关联服务对象 -->
        <association property="serve" javaType="org.dromara.houseKeeping.domain.Serve" autoMapping="true" columnPrefix="serve_"/>
        <!-- 关联服务人员对象 -->
        <association property="staff" javaType="org.dromara.houseKeeping.domain.Staff" autoMapping="true" columnPrefix="staff_"/>
    </resultMap>

    <sql id="selectHkOrderVo">
        select
            ho.order_id, ho.reservation_id, ho.customer_id, ho.serve_id, ho.staff_id,
            ho.order_amount, ho.status, ho.create_time,
            -- 预约信息
            r.reservation_id as reservation_reservation_id, r.serve_id as reservation_serve_id,
            r.service_date as reservation_service_date, r.contact_name as reservation_contact_name,
            r.service_time as reservation_service_time, r.phone as reservation_phone,
            r.address as reservation_address, r.special as reservation_special,
            r.status as reservation_status, r.staff_id as reservation_staff_id,
            r.customer_id as reservation_customer_id, r.create_time as reservation_create_time,
            -- 顾客信息
            u.user_id as customer_user_id, u.user_name as customer_user_name,
            u.nick_name as customer_nick_name, u.user_type as customer_user_type,
            u.email as customer_email, u.phonenumber as customer_phonenumber,
            u.sex as customer_sex, u.avatar as customer_avatar,
            u.status as customer_status, u.create_time as customer_create_time,
            -- 服务信息
            s.serve_id as serve_serve_id, s.name as serve_name, s.category as serve_category,
            s.image as serve_image, s.description as serve_description,
            s.description_detail as serve_description_detail, s.price as serve_price,
            s.unit as serve_unit, s.service_unit as serve_service_unit,
            s.status as serve_status, s.is_push as serve_is_push,
            s.view_count as serve_view_count, s.order_count as serve_order_count,
            s.tag as serve_tag, s.star as serve_star, s.create_time as serve_create_time,
            -- 服务人员信息
            st.staff_id as staff_staff_id, st.name as staff_name, st.title as staff_title,
            st.gender as staff_gender, st.phone as staff_phone, st.age as staff_age,
            st.star as staff_star, st.image as staff_image, st.category as staff_category,
            st.job as staff_job, st.cert as staff_cert, st.status as staff_status,
            st.tag as staff_tag, st.experience as staff_experience,
            st.experience_detail as staff_experience_detail, st.user_id as staff_user_id,
            st.auth as staff_auth, st.create_time as staff_create_time
        from hk_order ho
        left join reservation r on ho.reservation_id = r.reservation_id
        left join sys_user u on ho.customer_id = CAST(u.user_id AS CHAR)
        left join serve s on ho.serve_id = s.serve_id
        left join staff st on ho.staff_id = st.staff_id
    </sql>
    <select id="queryList" parameterType="org.dromara.houseKeeping.domain.query.HkOrderQuery" resultMap="HkOrderResultVo">
        <include refid="selectHkOrderVo"/>
        <where>
            <if test="reservationId != null and reservationId != ''"> and ho.reservation_id like concat(concat('%', #{reservationId}), '%')</if>
            <if test="customerId != null and customerId != ''"> and ho.customer_id like concat(concat('%', #{customerId}), '%')</if>
            <if test="serveId != null and serveId != ''"> and ho.serve_id like concat(concat('%', #{serveId}), '%')</if>
            <if test="staffId != null and staffId != ''"> and ho.staff_id like concat(concat('%', #{staffId}), '%')</if>
        </where>
    </select>

    <select id="selectVoHkOrderById" parameterType="java.lang.String" resultMap="HkOrderResultVo">
        <where>
            <if test="orderId != null and orderId != ''"> and ho.order_id like concat(concat('%', #{orderId}), '%')</if>
        </where>
    </select>

</mapper>
