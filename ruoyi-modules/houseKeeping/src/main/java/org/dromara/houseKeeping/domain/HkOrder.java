package org.dromara.houseKeeping.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.math.BigDecimal;
import java.io.Serial;
import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;

/**
 * 支付订单对象 hk_order
 *
 * <AUTHOR>
 * @date 2025-07-31
 */
@Data
@TableName("hk_order")
public class HkOrder implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 订单编号
     */
    @TableId(value = "order_id")
    private String orderId;

    /**
     * 预约编号
     */
    private String reservationId;

    /**
     * 顾客编号
     */
    private String customerId;

    /**
     * 服务编号
     */
    private String serveId;

    /**
     * 服务人员编号
     */
    private String staffId;

    /**
     * 订单金额
     */
    private BigDecimal orderAmount;

    /**
     * 订单状态
     */
    private String status;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

}
