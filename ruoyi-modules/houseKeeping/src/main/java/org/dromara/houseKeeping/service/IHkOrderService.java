package org.dromara.houseKeeping.service;

import org.dromara.houseKeeping.domain.HkOrder;
import org.dromara.houseKeeping.domain.bo.HkOrderBo;
import org.dromara.houseKeeping.domain.query.HkOrderQuery;
import org.dromara.houseKeeping.domain.vo.HkOrderVo;
import com.baomidou.mybatisplus.extension.service.IService;
import org.dromara.common.mybatis.core.page.TableDataInfo;

import java.util.Collection;
import java.util.List;

/**
 * 支付订单Service接口
 *
 * <AUTHOR>
 * @date 2025-07-31
 */
public interface IHkOrderService extends IService<HkOrder> {

    /**
     * 查询支付订单
     *
     * @param orderId 主键
     * @return HkOrderVo
     */
    HkOrderVo queryById(String orderId);

    /**
     * 分页查询支付订单列表
     *
     * @param query 查询对象
     * @return 支付订单分页列表
     */
    TableDataInfo<HkOrderVo> queryPageList(HkOrderQuery query);

    /**
     * 查询支付订单列表
     *
     * @param query 查询对象
     * @return 支付订单列表
     */
    List<HkOrderVo> queryList(HkOrderQuery query);

    /**
     * 新增支付订单
     *
     * @param bo 支付订单新增业务对象
     * @return 是否新增成功
     */
    Boolean insertByBo(HkOrderBo bo);

    /**
     * 修改支付订单
     *
     * @param bo 支付订单编辑业务对象
     * @return 是否修改成功
     */
    Boolean updateByBo(HkOrderBo bo);

    /**
     * 批量删除支付订单信息
     *
     * @param ids 待删除的主键集合
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<String> ids);
}
