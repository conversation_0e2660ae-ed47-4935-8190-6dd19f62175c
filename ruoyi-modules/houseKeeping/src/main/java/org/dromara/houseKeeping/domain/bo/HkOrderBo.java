package org.dromara.houseKeeping.domain.bo;

import org.dromara.houseKeeping.domain.HkOrder;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.hibernate.validator.constraints.Length;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;
import java.math.BigDecimal;

import java.util.Date;
import java.io.Serial;
import java.io.Serializable;

/**
 * 支付订单业务对象 hk_order
 *
 * <AUTHOR>
 * @date 2025-07-31
 */
@Data
@AutoMapper(target = HkOrder.class, reverseConvertGenerate = false)
public class HkOrderBo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 订单编号
     */
    @NotBlank(message = "订单编号不能为空", groups = {EditGroup.class})
    @Length(max = 32, message = "订单编号不能大于{max}个字符", groups = {EditGroup.class})
    private String orderId;
    /**
     * 预约编号
     */
    @Length(max = 32, message = "预约编号不能大于{max}个字符", groups = {AddGroup.class, EditGroup.class})
    private String reservationId;
    /**
     * 顾客编号
     */
    @Length(max = 32, message = "顾客编号不能大于{max}个字符", groups = {AddGroup.class, EditGroup.class})
    private String customerId;
    /**
     * 服务编号
     */
    @Length(max = 32, message = "服务编号不能大于{max}个字符", groups = {AddGroup.class, EditGroup.class})
    private String serveId;
    /**
     * 服务人员编号
     */
    @Length(max = 32, message = "服务人员编号不能大于{max}个字符", groups = {AddGroup.class, EditGroup.class})
    private String staffId;
    /**
     * 订单金额
     */
    private BigDecimal orderAmount;
    /**
     * 订单状态
     */
    @Length(max = 20, message = "订单状态不能大于{max}个字符", groups = {AddGroup.class, EditGroup.class})
    private String status;
}
