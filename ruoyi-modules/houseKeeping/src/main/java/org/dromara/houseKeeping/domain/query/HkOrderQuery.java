package org.dromara.houseKeeping.domain.query;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

import java.math.BigDecimal;
import org.dromara.common.mybatis.core.domain.BasePageQuery;

/**
 * 支付订单查询对象 hk_order
 *
 * <AUTHOR>
 * @date 2025-07-31
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class HkOrderQuery extends BasePageQuery {

    /**
     * 预约编号
     */
    private String reservationId;

    /**
     * 顾客编号
     */
    private String customerId;

    /**
     * 服务编号
     */
    private String serveId;

    /**
     * 服务人员编号
     */
    private String staffId;

}
