package org.dromara.houseKeeping.mapper;

import org.dromara.houseKeeping.domain.HkOrder;
import org.dromara.houseKeeping.domain.bo.HkOrderBo;
import org.dromara.houseKeeping.domain.query.HkOrderQuery;
import org.dromara.houseKeeping.domain.vo.HkOrderVo;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;

import java.util.List;

/**
 * 支付订单Mapper接口
 *
 * <AUTHOR>
 * @date 2025-07-31
 */
public interface HkOrderMapper extends BaseMapperPlus<HkOrder, HkOrderVo> {

    /**
     * 查询支付订单列表
     *
     * @param query 查询对象
     * @return {@link HkOrderVo}
     */
    List<HkOrderVo> queryList(HkOrderQuery query);
}
