package org.dromara.houseKeeping.domain.vo;

import java.math.BigDecimal;
import org.dromara.houseKeeping.domain.HkOrder;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import java.util.Date;
import java.io.Serial;
import java.io.Serializable;

/**
 * 支付订单视图对象 hk_order
 *
 * <AUTHOR>
 * @date 2025-07-31
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = HkOrder.class)
public class HkOrderVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 订单编号
     */
    @ExcelProperty(value = "订单编号")
    private String orderId;

    /**
     * 预约编号
     */
    @ExcelProperty(value = "预约编号")
    private String reservationId;

    /**
     * 顾客编号
     */
    @ExcelProperty(value = "顾客编号")
    private String customerId;

    /**
     * 服务编号
     */
    @ExcelProperty(value = "服务编号")
    private String serveId;

    /**
     * 服务人员编号
     */
    @ExcelProperty(value = "服务人员编号")
    private String staffId;

    /**
     * 订单金额
     */
    @ExcelProperty(value = "订单金额")
    private BigDecimal orderAmount;

    /**
     * 订单状态
     */
    @ExcelProperty(value = "订单状态")
    private String status;

    /**
     * 创建时间
     */
    @ExcelProperty(value = "创建时间")
    private Date createTime;

}
