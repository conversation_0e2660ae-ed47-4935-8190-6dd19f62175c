<template>
  <div class="luxury-hotel">
    <!-- 导航栏 -->
    <nav class="navbar" :class="{ 'scrolled': isScrolled }">
      <div class="nav-container">
        <div class="logo">
          <h2>GRAND PALACE</h2>
        </div>
        <ul class="nav-menu">
          <li><a href="#home">首页</a></li>
          <li><a href="#rooms">客房</a></li>
          <li><a href="#dining">餐饮</a></li>
          <li><a href="#spa">水疗</a></li>
          <li><a href="#events">会议</a></li>
          <li><a href="#contact">联系我们</a></li>
        </ul>
        <button class="book-btn">立即预订</button>
      </div>
    </nav>

    <!-- 主横幅区域 -->
    <section class="hero-section" id="home">
      <div class="hero-slider">
        <div class="hero-slide active">
          <div class="hero-content">
            <h1 class="hero-title">奢华体验，尊贵享受</h1>
            <p class="hero-subtitle">在这里，每一刻都是完美的开始</p>
            <div class="hero-buttons">
              <button class="btn-primary">探索客房</button>
              <button class="btn-secondary">查看优惠</button>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 预订表单 -->
      <div class="booking-form">
        <div class="form-group">
          <label>入住日期</label>
          <input type="date" v-model="checkIn">
        </div>
        <div class="form-group">
          <label>退房日期</label>
          <input type="date" v-model="checkOut">
        </div>
        <div class="form-group">
          <label>客房数量</label>
          <select v-model="rooms">
            <option value="1">1间客房</option>
            <option value="2">2间客房</option>
            <option value="3">3间客房</option>
          </select>
        </div>
        <div class="form-group">
          <label>客人数量</label>
          <select v-model="guests">
            <option value="1">1位客人</option>
            <option value="2">2位客人</option>
            <option value="3">3位客人</option>
            <option value="4">4位客人</option>
          </select>
        </div>
        <button class="search-btn">搜索客房</button>
      </div>
    </section>

    <!-- 特色服务 -->
    <section class="features-section">
      <div class="container">
        <h2 class="section-title">尊享服务</h2>
        <div class="features-grid">
          <div class="feature-card" v-for="feature in features" :key="feature.id">
            <div class="feature-icon">
              <i :class="feature.icon"></i>
            </div>
            <h3>{{ feature.title }}</h3>
            <p>{{ feature.description }}</p>
          </div>
        </div>
      </div>
    </section>

    <!-- 客房展示 -->
    <section class="rooms-section" id="rooms">
      <div class="container">
        <h2 class="section-title">精选客房</h2>
        <div class="rooms-grid">
          <div class="room-card" v-for="room in rooms" :key="room.id">
            <div class="room-image">
              <div class="room-overlay">
                <button class="view-btn">查看详情</button>
              </div>
            </div>
            <div class="room-info">
              <h3>{{ room.name }}</h3>
              <p>{{ room.description }}</p>
              <div class="room-price">
                <span class="price">¥{{ room.price }}</span>
                <span class="per-night">/晚</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 页脚 -->
    <footer class="footer">
      <div class="container">
        <div class="footer-content">
          <div class="footer-section">
            <h3>GRAND PALACE</h3>
            <p>为您提供无与伦比的奢华住宿体验</p>
          </div>
          <div class="footer-section">
            <h4>联系信息</h4>
            <p>电话: +86 400-123-4567</p>
            <p>邮箱: <EMAIL></p>
            <p>地址: 北京市朝阳区建国门外大街1号</p>
          </div>
          <div class="footer-section">
            <h4>关注我们</h4>
            <div class="social-links">
              <a href="#"><i class="fab fa-weibo"></i></a>
              <a href="#"><i class="fab fa-wechat"></i></a>
              <a href="#"><i class="fab fa-instagram"></i></a>
            </div>
          </div>
        </div>
        <div class="footer-bottom">
          <p>&copy; 2024 Grand Palace Hotel. 保留所有权利.</p>
        </div>
      </div>
    </footer>
  </div>
</template>

<script>
export default {
  name: 'LuxuryHotel',
  data() {
    return {
      isScrolled: false,
      checkIn: '',
      checkOut: '',
      rooms: '1',
      guests: '2',
      features: [
        {
          id: 1,
          icon: 'fas fa-concierge-bell',
          title: '24小时礼宾服务',
          description: '专业礼宾团队随时为您提供贴心服务'
        },
        {
          id: 2,
          icon: 'fas fa-spa',
          title: '豪华水疗中心',
          description: '享受世界级的水疗护理和放松体验'
        },
        {
          id: 3,
          icon: 'fas fa-utensils',
          title: '米其林餐厅',
          description: '品尝由顶级厨师精心制作的美食'
        },
        {
          id: 4,
          icon: 'fas fa-car',
          title: '豪华接送服务',
          description: '提供机场接送和城市观光服务'
        }
      ],
      rooms: [
        {
          id: 1,
          name: '豪华海景套房',
          description: '180度海景视野，私人阳台，独立客厅',
          price: '2888'
        },
        {
          id: 2,
          name: '总统套房',
          description: '顶层奢华套房，私人管家服务',
          price: '8888'
        },
        {
          id: 3,
          name: '行政客房',
          description: '商务出行首选，配备办公区域',
          price: '1588'
        }
      ]
    }
  },
  mounted() {
    window.addEventListener('scroll', this.handleScroll)
  },
  beforeUnmount() {
    window.removeEventListener('scroll', this.handleScroll)
  },
  methods: {
    handleScroll() {
      this.isScrolled = window.scrollY > 50
    }
  }
}
</script>